#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-11-18
<AUTHOR>   Ku<PERSON>Lu
@Email      :   <EMAIL>
@File       :   DQueue.py
@Software   :   PyCharm
"""
import queue
from pathlib import Path


def data_queue(param_file: Path) -> queue.Queue:
    """
    创建数据队列
    :param param_file: 参数化文件（绝对路径）
    :return: user_data_queue
    """
    with param_file.open('r', encoding='utf-8') as f:
        param_list = f.readlines()
    key_list = param_list[0].strip().split(',')
    param_dict_in_list = []
    for i, v in enumerate(param_list):
        if i > 0:
            value_list = v.strip().split(',')
            if len(key_list) == len(value_list):
                param_dict_in_list.append(dict(zip(key_list, value_list)))
    user_data_queue = queue.Queue()
    if param_dict_in_list:
        for data in param_dict_in_list:
            user_data_queue.put_nowait(data)
    return user_data_queue
