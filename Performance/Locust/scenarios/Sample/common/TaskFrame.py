#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-12-03
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   TaskFrame.py
@Software   :   PyCharm
"""
import json
from typing import Mapping, Any, List


class FuncFrame:

    def __init__(self, url: str, method: str, name: str, headers: Mapping[str:Any],
                 body: Mapping[str:Any], assert_keys: List[str]):
        """接口信息"""
        self.url = url
        self.method = method.upper()
        self.name = name
        self.headers = headers
        self.body = body
        self.assert_keys = assert_keys

    def demo_task(self, obj):
        """任务模板"""
        data = json.dumps(self.body) if 'json' in str(self.headers) else self.body
        with obj.client.request(method=self.method, name=self.name, url=self.url, headers=self.headers, data=data,
                                catch_response=True) as response:
            check_status_code = str(response.status_code) == '200'
            assert_value = all(each_key in response.content.decode() for each_key in self.assert_keys)
            response.success() if check_status_code and assert_value else response.failure(
                f"FailTrans is: [ {self.name} ]; "
                f"FailTrans url is: [ {self.url} ]; "
                f"Fail response code is: [ {response.status_code} ]; "
                f"Fail response body is: {response.content.decode()}")
