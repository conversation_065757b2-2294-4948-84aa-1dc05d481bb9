# -*- coding: utf-8 -*-
"""
@Date       :   2022-07-13
<AUTHOR>   <PERSON><PERSON><PERSON>u
@Email      :   <EMAIL>
@File       :   demo_semaphore_rendezvous.py
@Software   :   PyCharm
"""
import os

from locust import HttpUser, TaskSet, task, between, events
from gevent._semaphore import Semaphore

all_locusts_spawned = Semaphore()
all_locusts_spawned.acquire()  # 阻塞线程


# 挂载到locust钩子函数（所有的Locust示例产生完成时触发）
@events.spawning_complete.add_listener
def on_hatch_complete(**kwargs):
    """
    Select_task类的钩子方法
    :param kwargs:
    :return:
    """
    all_locusts_spawned.release()  # 创建钩子方法


# events.spawning_complete.add_listener(on_hatch_complete)

n = 0


class UserBehavior(TaskSet):

    def login(self):
        global n
        n += 1
        print("%s个虚拟用户开始启动，并登录" % n)

    def logout(self):
        print("退出登录")

    def on_start(self):
        self.login()
        all_locusts_spawned.wait()  # 同步锁等待

    @task(4)
    def test1(self):
        url = '/list'
        param = {
            "limit": 8,
            "offset": 0,
        }
        with self.client.get(url, params=param, headers={}, catch_response=True) as response:
            print("用户浏览登录首页")

    @task(6)
    def test2(self):
        url = '/detail'
        param = {
            'id': 1
        }
        with self.client.get(url, params=param, headers={}, catch_response=True) as response:
            print("用户同时执行查询")

    @task(1)
    def test3(self):
        """
        用户查看查询结果
        :return:
        """
        url = '/order'
        param = {
            "limit": 8,
            "offset": 0,
        }
        with self.client.get(url, params=param, headers={}, catch_response=True) as response:
            print("用户查看查询结果")

    def on_stop(self):
        self.logout()


class WebsiteUser(HttpUser):
    host = 'http://www.baidu.com'
    tasks = [UserBehavior]

    wait_time = between(1, 2)


if __name__ == '__main__':
    os.system("locust -f demo_semaphore_rendezvous.py")
