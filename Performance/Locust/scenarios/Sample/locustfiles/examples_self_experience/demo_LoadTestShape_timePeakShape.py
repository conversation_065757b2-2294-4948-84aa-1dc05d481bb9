#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-11-29
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_LoadTestShape_timePeakShape
@Software   :   PyCharm
"""

import datetime
from locust import LoadTestShape, task, constant, HttpUser


# 启动策略：每隔10秒钟增加启动10个用户
class MyUser(HttpUser):
    wait_time = constant(1)

    @task(1)
    def task_1(self):
        print("my_task!!!")


class MyCustomShape(LoadTestShape):
    # time_limit设置时限整个压测过程为60秒
    time_limit = 60
    # 设置产生率一次启动10个用户
    spawn_rate = 10

    def tick(self):
        """
        设置 tick()函数
        并在tick()里面调用 get_run_time()方法
        """
        # 调用get_run_time()方法获取压测执行的时间
        run_time = self.get_run_time()
        # 运行时间在 time_limit之内，则继续执行
        if run_time < self.time_limit:
            # user_count计算每10秒钟增加10个
            user_count = round(run_time, -1)
            print(str(user_count) + ">>>>>" + datetime.datetime.now().strftime('%Y-%m-%d-%H:%M:%S.%f'))
            return user_count, self.spawn_rate
        return None
