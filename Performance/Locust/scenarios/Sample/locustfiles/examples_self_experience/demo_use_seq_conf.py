#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-08-27
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_use_seq_conf
@Software   :   PyCharm
"""
import os
import json
from pathlib import Path
from datetime import datetime
from locust import HttpUser, SequentialTaskSet, task, constant


class UserBehavior(SequentialTaskSet):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.study_env_site_id = "c8b894d0-22c7-4b06-93c5-502c090a0d5b"
        self.env_id = "f82a3e99-5da3-4942-8ccd-f69216ed90dd"
        self.user_id = "091d7cd8-b5c3-475c-a7f5-27c5cb6308bb"
        self.headers = {
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "Yc-Role-Id": "2259f726-7673-4018-a74c-be11c14a8f24",
            "Yc-Study-Id": "7fcb6234-6eef-4e71-a281-6cd3e53f9211",
            "Cookie": "usr=*******************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }

    def on_start(self):
        print("**********测试准备执行——测试功能：添加受试者**********")

    def on_stop(self):
        print("******************** 测试结束 ********************")

    @task(1)
    def get_next_screen_key(self):
        """获取下一个 screen_key"""
        url = f"/api/edc/subject/get_next_screen_key/{self.study_env_site_id}"
        with self.client.post(name="get_next_screen_key", url=url, headers=self.headers,
                              catch_response=True) as response:
            # assert 'screen_key' in response  # 断言，判断接口返回是否成功
            response.success() if 'screen_key' in response.content.decode() else response.failure(
                f"FailTrans is: [get_next_screen_key]\nFail msg is:\n{response}")

    @task(1)
    def add_subject(self):
        """添加受试者"""
        ms_ts = datetime.now().strftime("%H%M%S%f")[0:-3]
        screen_key = f"S-Protocol_AA-KC_AA-{ms_ts}"
        url = "/api/edc/subject/add"
        body = {
            "activeness": "ACTIVE",
            "screen_key": f"{screen_key}",
            "study_site_id": f"{self.study_env_site_id}",
            "user_id": f"{self.user_id}"
        }
        with self.client.post(name="add_subject", url=url, headers=self.headers, data=json.dumps(body),
                              catch_response=True) as response:
            response.success() if f'{screen_key}' in response.content.decode() else response.failure(
                f"FailTrans is: [add_subject]\nFail msg is:\n{response}")

    @task(1)
    def list_orchestration(self):
        """list orchestration"""
        url = "/api/edc/orchestration/list"
        body = {
            "environment_id": f"{self.env_id}",
            "filter": {
                "activenesses": [
                    "ACTIVE"
                ],
                "site_ids": [
                    f"{self.study_env_site_id}"
                ]
            },
            "page_index": 1,
            "page_size": 10,
            "stats_filter": []
        }
        with self.client.post(name="list_orchestration", url=url, headers=self.headers, data=json.dumps(body),
                              catch_response=True) as response:
            response.success() if f'{self.study_env_site_id}' in response.content.decode() else response.failure(
                f"FailTrans is: [list_orchestration]\nFail msg is:\n{response}")

    @task(1)
    def get_study_environment_lock_status(self):
        """获取环境中 study 锁状态"""
        url = f"/api/edc/lock/get_study_environment_lock_status/{self.env_id}"
        with self.client.post(name="get_study_environment_lock_status", url=url, headers=self.headers,
                              catch_response=True) as response:
            response.success() if response.status_code == 200 else response.failure(
                f"FailTrans is: [get_study_environment_lock_status]\nFail msg is:\n{response}")

    @task(1)
    def get_study(self):
        """获取 study 信息"""
        url = "/api/edc/study/get"
        body = {}
        with self.client.post(name="get_study", url=url, headers=self.headers, data=body,
                              catch_response=True) as response:
            response.success() if response.status_code == 200 else response.failure(
                f"FailTrans is: [get_study]\nFail msg is:\n{response}")

    tasks = [list_orchestration]


class PtUser(HttpUser):
    host = "https://your_url.com"
    tasks = [UserBehavior]
    wait_time = constant(0)


if __name__ == '__main__':
    path_info = ["conf", "locust.conf"]
    conf_abs_file = Path.cwd().parent.joinpath(*path_info)
    os.system(f'locust --config {conf_abs_file}')
