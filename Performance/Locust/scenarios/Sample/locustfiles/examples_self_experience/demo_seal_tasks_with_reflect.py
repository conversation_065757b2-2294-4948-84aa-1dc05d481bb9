#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-12-03
<AUTHOR>   <PERSON>noLu
@Email      :   <EMAIL>
@File       :   demo_seal_tasks_with_reflect.py
@Software   :   PyCharm
"""
import os
import sys

root_local_path = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
root_gen_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(root_local_path)

from pathlib import Path
from locust import HttpUser, TaskSet, task, events, tag, constant
from Locust.scenario.Sample.common.TaskFrame import FuncFrame
# from Demo.common.TaskFrame import FuncFrame
from locust_plugins import listeners
import locust_plugins

os.environ['PGHOST'] = '*************'
os.environ['PGPORT'] = '5432'
os.environ['PGUSER'] = 'postgres'
os.environ['PGPASSWORD'] = 'kuno@123456'
os.environ['PGDATABASE'] = 'perf'

locust_data = [
    {
        "task_name": "get_aggregated_stats",
        "task_info": {
            "display_name": "get_aggregated_stats",
            "url": "/api/edc/casebook_stats/get_aggregated_stats",
            "method": "post",
            "headers": {
                "Connection": "keep-alive",
                "Content-Type": "application/json",
            },
            "data": {},
            "assert_keys": ["msg"],
            "assert_value": "success",
            "task": 1
        }
    }
]


class Tasks:
    """任务类"""


class UserTask(TaskSet):
    task_name = locust_data[0]['task_name']
    all_data = locust_data[0]['task_info']
    # 通过反射机制循环添加任务
    setattr(Tasks, task_name,
            FuncFrame(url=all_data['url'], method=all_data['method'], name=all_data['display_name'],
                      headers=all_data['headers'], body=all_data['data'],
                      assert_keys=all_data['assert_keys']).demo_task)
    # 循环设置task权重
    tasks = {}
    for each_task in locust_data:
        tasks[getattr(Tasks, task_name)] = each_task['task']
    # 注意：如果使用 SequentialTaskSet, 则 tasks 必须是 list
    # tasks = []
    # for each_task in locust_data:
    #     tasks.append(getattr(Tasks, task_name))


class User(HttpUser):
    tasks = [UserTask]
    wait_time = constant(1)


@events.init.add_listener
def on_locust_init(environment, **_kwargs):
    listeners.Timescale(env=environment, testplan="load-ssc_display")


if __name__ == '__main__':
    path_info = ["conf", "locust.conf"]
    conf_abs_file = Path.cwd().joinpath(*path_info)
    # os.system(f'locust --config {conf_abs_file}')
