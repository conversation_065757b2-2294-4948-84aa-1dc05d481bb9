#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-11-29
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_LoadTestShape_timeQuantumShape
@Software   :   PyCharm
"""
import datetime
from locust import LoadTestShape, task, constant, HttpUser

"""
启动策略：
前10秒加载10个用户,10秒～30秒,用户数为10;
30秒到40秒加载20个用户,40秒～60秒启动总用户数是30;
60秒到70秒加载30个用户,70秒～600秒启动总用户数是60;
600秒到610秒加载20个用户,大于610秒启动总用户数是80个用户
"""


class MyUser(HttpUser):
    wait_time = constant(1)

    @task(1)
    def task_1(self):
        print("my_task!!!")


class MyCustomShape(LoadTestShape):
    """
        time -- 持续时间，经过多少秒后，进入到下个阶段
        users -- 总用户数
        spawn_rate -- 用户加载率(即多少秒加载完指定用户数)
    """

    stages = [
        {"time": 10, "users": 10, "spawn_rate": 10},
        {"time": 30, "users": 30, "spawn_rate": 10},
        {"time": 60, "users": 60, "spawn_rate": 10},
        {"time": 600, "users": 80, "spawn_rate": 10},
    ]

    def tick(self):
        run_time = self.get_run_time()
        for stage in self.stages:
            if run_time < stage['time']:
                tick_data = (stage['users'], stage['spawn_rate'])
                return tick_data
        return None
