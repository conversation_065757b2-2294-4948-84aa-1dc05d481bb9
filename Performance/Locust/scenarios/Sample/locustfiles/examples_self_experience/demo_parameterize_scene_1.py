#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-08-17
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   demo_parameterize_scene_1
@Software   :   PyCharm
"""

'''
模拟3个用户并发请求网页，共有100个URL地址，每个虚拟用户都会依次循环加载100个URL地址
'''

from locust import TaskSet, task, HttpUser, between


class UserBehav(TaskSet):
    def on_start(self):
        self.index = 0
        self.share_data = ['url1', 'url2', 'url3', 'url4', 'url5']

    @task
    def test_visit(self):
        url = self.share_data[self.index]
        print('visit url: %s' % url)
        self.index = (self.index + 1) % len(self.share_data)
        self.client.get(url)


class WebsiteUser(HttpUser):
    host = 'http://www.xxx.com'
    task_set = task(UserBehav)
    wait_time = between(1.0, 15.0)
