########### global param ############
locustfile = scene_kenta_prescriptions.py
# host = https://www.yakumaru.ai
host = https://medical-hp-dev.moair.net
# All tags: []
# tags = []
# exclude-tags = []
exit-code-on-error = 1
#####################################

######### web UI mode param #########
# web-host = 127.0.0.1
# web-port = 9333
#####################################

####### no web UI mode param ########
headless = true
# users = 120
# spawn-rate = 0.2
# run-time = 30m
# iterations = 2
# csv = result/scene_kenta_prescriptions
# csv-full-history = true
# html = result/scene_kenta_prescriptions.html
#####################################

####### global optional param #######
print-stats = true
only-summary = true
### log相关参数配置后 stdout/stderr 均不显示日志
logfile = run.log
loglevel = INFO
#####################################

########### special param ###########
### spawn 完成后重置统计信息。
### 在分布式模式下运行时，应在 master 和 worker 上都设置。
reset-stats = true
### 退出前等待模拟用户完成任何正在执行的任务的秒数。默认是立即终止。
### 该参数在分布式模式下，只需要在 master 进程中配置即可。
stop-timeout = 5
#####################################