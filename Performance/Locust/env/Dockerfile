ARG LOCUST_VERSION

FROM locustio/locust:${LOCUST_VERSION}

ARG LOCUST_COMMON_WHL
ARG NEOX_LOCUST_WHL

COPY ./lib/${LOCUST_COMMON_WHL} /tmp/${LOCUST_COMMON_WHL}
COPY ./lib/${NEOX_LOCUST_WHL} /tmp/${NEOX_LOCUST_WHL}

ARG BUILD_DATE

LABEL maintainer="KunoLu" \
    version="locust-2.32.5" \
    name="Customized Locust Image By NeoX" \
    date="${BUILD_DATE}" \
    description="Install locust-plugins & etc."

RUN pip3 install \
    locust-plugins==4.5.3 \
    psycogreen==1.0.2 \
    psycopg2-binary==2.9.9 \
    aenum==3.1.15 \
    demjson3==3.0.6 \
    jmespath==1.0.1 \
    loguru==0.7.2 \
    orjson==3.10.7 \
    pendulum==3.0.0 \
    pymongo==4.10.1 \
    python-dotenv==1.0.1 \
    pyyaml==6.0.2 \
    requests_toolbelt==1.0.0 \
    toml==0.10.2 \
    websockets==13.1 \
    websocket-client==1.8.0 \
    /tmp/${LOCUST_COMMON_WHL} \
    /tmp/${NEOX_LOCUST_WHL}

# RUN rm /tmp/${LOCUST_COMMON_WHL} /tmp/${NEOX_LOCUST_WHL}
