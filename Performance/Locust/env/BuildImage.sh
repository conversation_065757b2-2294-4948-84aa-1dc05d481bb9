#!/bin/bash

# 导入环境变量
export $(cat .env | xargs)

# 清理使用正则匹配到的容器
IMAGE_IDS=$(docker images --format "{{.Repository}}:{{.Tag}} {{.ID}}" | grep "${IMAGE_NAME}" | awk '{print $2}' | sort -u)

CONTAINER_IDS=""
for ID in $IMAGE_IDS; do
  CONTAINER_IDS="$CONTAINER_IDS $(docker ps -a -q --filter ancestor=$ID)"
done

if [ -n "$CONTAINER_IDS" ]; then
  # echo "Deleting containers: $CONTAINER_IDS"
  echo $CONTAINER_IDS | tr ' ' '\n' | sort -u | xargs docker rm -f 2>/dev/null || true
fi

# 删除所有版本的同名镜像
docker images ${IMAGE_NAME} --format "{{.ID}}" | sort -u | xargs docker rmi -f 2>/dev/null || true

# 构建镜像
docker build \
  --build-arg LOCUST_VERSION=${LOCUST_VERSION} \
  --build-arg LOCUST_COMMON_WHL=${LOCUST_COMMON_WHL} \
  --build-arg NEOX_LOCUST_WHL=${NEOX_LOCUST_WHL} \
  --build-arg BUILD_DATE=$(date -u +'%Y-%m-%d') \
  -t ${IMAGE_NAME}:${IMAGE_TAG} .
