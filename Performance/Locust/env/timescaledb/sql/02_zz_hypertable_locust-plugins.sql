-- this needs to be in a different file/session, so that timescale extension is properly initialized

SELECT create_hypertable('request', 'time');

--
-- add retention policy for request hypertable
--
SELECT add_retention_policy('request', INTERVAL '90 days');


--
-- check the retention policy of request hypertable
--
-- SELECT * FROM timescaledb_information.jobs 
-- WHERE hypertable_name = 'request' 
-- AND proc_name = 'policy_retention';

--
-- mannually delete data older than 90 days from request hypertable
--
-- DELETE FROM request WHERE time < NOW() - INTERVAL '90 days';