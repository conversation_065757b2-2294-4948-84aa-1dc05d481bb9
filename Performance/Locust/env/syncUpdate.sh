#!/bin/bash

# 定义命令
commands=(
    "cd /home/<USER>/bitbucket/testing && git stash && git pull"
    "sudo rsync -av --delete /home/<USER>/bitbucket/testing/Performance/Locust/ /home/<USER>/docker-compose/locust/NeoX/"
    "cp -f /home/<USER>/docker-compose/locust/NeoX/env/.env /home/<USER>/docker-compose/locust/NeoX/env/docker-compose.yml /home/<USER>/docker-compose/locust/"
    "cp -f /home/<USER>/docker-compose/locust/NeoX/preparation/genParam/output/csv/*pres*.csv /home/<USER>/docker-compose/locust/NeoX/scenarios/Kenta/BE-Prescriptions/paramlist/"
    "cp -f /home/<USER>/docker-compose/locust/NeoX/preparation/genParam/output/csv/*enquete*.csv /home/<USER>/docker-compose/locust/NeoX/scenarios/Gateway/Enquete/paramlist/"
)

# 遍历命令并执行
for cmd in "${commands[@]}"; do
    echo "正在执行命令: $cmd"
    eval $cmd
    # 检查上一个命令的返回码
    if [ $? -eq 0 ]; then
        echo "命令 $cmd 执行成功"
    else
        echo "错误: 命令 $cmd 执行失败"
        exit 1
    fi
done

echo "所有命令执行成功"
exit 0
