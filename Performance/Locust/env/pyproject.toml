[project]
name = "neox-performance-testing"
version = "0.1.0"
description = "Performance testing dependencies for NeoX using Locust."
authors = [{ name = "Ku<PERSON> Lu", email = "<EMAIL>" }]
# 仅用于依赖管理，不作为可安装包
requires-python = ">=3.12"

# Core dependencies found in both requirements files.
dependencies = [
    "locust==2.37.10",
    "locust-plugins==4.7.0",
    "psycogreen==1.0.2",
    "psycopg2-binary==2.9.10",
    "aenum==3.1.16",
    "demjson3==3.0.6",
    "jmespath==1.0.1",
    "loguru==0.7.3",
    "orjson==3.10.18",
    "pendulum==3.1.0",
    "pymongo==4.13.1",
    "python-dotenv==1.1.0",
    "PyYAML==6.0.2",
    "requests-toolbelt==1.0.0",
    "toml==0.10.2",
    "websockets==15.0.1",
    "websocket-client==1.8.0",
]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/"

# Optional dependencies to separate "latest" (CI) and "dev" environments.
# Install with:
# uv pip install -e .[latest]
# uv pip install -e .[dev]
[project.optional-dependencies]
latest = [
    # Paths are relative to this pyproject.toml file.
    "locust_common @ file:lib/locust_common-0.1.0-py3-none-any.whl",
    "neox_locust @ file:lib/neox_locust-0.1.0-py3-none-any.whl",
]

dev = [
    "pip-autoremove",
    "pipdeptree",
    # Editable installs for local development.
    # Paths are relative to this pyproject.toml file.
    "locust_common @ file:../../../lib/locust_common",
    "neox_locust @ file:../../../lib/neox_locust",
]
