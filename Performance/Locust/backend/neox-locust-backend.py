# -*- coding: utf-8 -*-
"""
@Time     :   2025/01/23 15:55:58
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email    :   <EMAIL>
@File     :   neox-locust-backend.py
@Software :   VSCode
"""

import os
from pathlib import Path

from dotenv import find_dotenv, load_dotenv
from locust_common.function.func_conf import (
    conf_to_dict,
    dict_to_conf,
    dump_toml,
    load_toml,
)
from sanic import Sanic
from sanic.exceptions import InvalidUsage
from sanic.response import json

load_dotenv(find_dotenv("Backend.env"), verbose=True, override=True)
TOML_FILE, CONF_FILE = Path(os.environ["TOML_FILE"]), Path(os.environ["CONF_FILE"])

app = Sanic("neox-locust-backend")


@app.route("/locust/config/get", methods=["GET"])
async def get_locust_config(request):
    """
    获取 locust 配置文件内容，返回 JSON 结构的配置数据\n
    Verification command:
        curl -X GET http://localhost:9333/locust/config/get
    :param request: /locust/config/get
    :return: JSONResponse
    """
    try:
        if TOML_FILE.exists():
            toml_content = load_toml(TOML_FILE)
        else:
            raise InvalidUsage("No toml file found.")
        if CONF_FILE.exists():
            conf_content = conf_to_dict(CONF_FILE)
        else:
            raise InvalidUsage("No conf file found.")

        return json({"status": True, "toml": toml_content, "conf": conf_content})

    except InvalidUsage as e:
        return json({"status": False, "error": str(e)}, status=400)


@app.route("/locust/config/set", methods=["POST"])
async def set_locust_config(request):
    """
    获取请求中的 JSON 数据，并写入配置文件，返回更新后的 JSON 结构的配置数据\n
    Verification command:
        curl -X POST http://localhost:9333/locust/config/set -H "Content-Type: application/json" -d "{\"toml\": {\"key\": \"value\"}, \"conf\": {\"key\": \"value\"}}"
    :param request: /locust/config/set
    :return: JSONResponse
    """
    try:
        # 验证请求头 Content-Type 是否为 application/json
        if request.headers.get("Content-Type") != "application/json":
            raise InvalidUsage("Content-Type must be application/json.")

        # 获取请求体中的 JSON 数据
        data = request.json

        if data is None:
            raise InvalidUsage("No JSON content found or invalid JSON format.")

        if "toml" not in data:
            raise InvalidUsage("No toml content found.")
        toml_content = data["toml"]
        # 将更新后的配置数据写入 TOML 文件
        _ = dump_toml(TOML_FILE, toml_content)

        if not CONF_FILE.exists():
            raise InvalidUsage("No conf file found.")
        conf_content = data.get("conf", None)
        # 如果 conf 的配置有更新，将更新后的配置数据写入 CONF 文件
        if conf_content:
            _ = dict_to_conf(CONF_FILE, conf_content)
        # 最后读取 CONF 文件的配置数据
        conf_content = conf_to_dict(CONF_FILE)

        # 处理 JSON 数据
        return json({"status": True, "toml": toml_content, "conf": conf_content})

    except InvalidUsage as e:
        return json({"status": False, "error": str(e)}, status=400)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=9333)
