[Unit]
Description=Sanic web application for neox-locust-backend
After=network.target

[Service]
User=ubuntu
WorkingDirectory=/home/<USER>/docker-compose/locust/NeoX/backend
ExecStart=sudo /root/miniconda3/envs/locust_v2.32.5/bin/python /home/<USER>/docker-compose/locust/NeoX/backend/neox-locust-backend.py
ExecStop=/usr/bin/kill -SIGTERM $MAINPID
Restart=always
RestartSec=1
StandardOutput=file:/var/log/neox-locust-backend.access.log
StandardError=file:/var/log/neox-locust-backend.error.log

[Install]
WantedBy=multi-user.target