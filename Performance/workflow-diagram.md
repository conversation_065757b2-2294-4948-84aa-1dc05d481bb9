# 性能测试工作流程

## 流程图

```mermaid
graph TD
    A[开始性能测试] --> B{选择部署方式}
    
    B -->|本地开发| C[本地环境设置]
    B -->|容器化部署| D[Docker 部署]
    B -->|生产环境| E[分布式部署]
    
    C --> C1[安装 uv 包管理器]
    C1 --> C2[创建虚拟环境]
    C2 --> C3[安装开发依赖]
    C3 --> F[配置测试场景]
    
    D --> D1[编辑 .env 配置]
    D1 --> D2[构建 Docker 镜像]
    D2 --> D3[启动 docker-compose]
    D3 --> F
    
    E --> E1[配置后端服务]
    E1 --> E2[启动 systemd 服务]
    E2 --> E3[验证服务状态]
    E3 --> F
    
    F --> G{选择测试模式}
    
    G -->|单进程| H[单进程模式]
    G -->|多进程| I[多进程模式]
    G -->|分布式| J[分布式模式]
    
    H --> H1[配置 locust.conf]
    I --> I1[配置 master/worker]
    J --> J1[配置多台机器]
    
    H1 --> K[选择测试场景]
    I1 --> K
    J1 --> K
    
    K --> K1{测试场景类型}
    
    K1 -->|网关测试| L1[Gateway 场景]
    K1 -->|系统测试| L2[Kenta 场景]
    K1 -->|识别引擎| L3[Recognition 场景]
    K1 -->|示例测试| L4[Sample 场景]
    
    L1 --> M[准备测试数据]
    L2 --> M
    L3 --> M
    L4 --> M
    
    M --> M1[参数化文件]
    M --> M2[Redis 数据]
    M --> M3[数据库数据]
    
    M1 --> N[启动测试]
    M2 --> N
    M3 --> N
    
    N --> O[实时监控]
    O --> O1[Web UI 监控]
    O --> O2[日志监控]
    O --> O3[系统资源监控]
    
    O1 --> P{测试状态}
    O2 --> P
    O3 --> P
    
    P -->|进行中| Q[继续监控]
    P -->|完成| R[生成报告]
    P -->|异常| S[故障排除]
    
    Q --> O
    
    S --> S1[检查日志]
    S1 --> S2[分析错误]
    S2 --> S3[调整配置]
    S3 --> N
    
    R --> R1[性能指标报告]
    R1 --> R2[错误统计报告]
    R2 --> R3[资源使用报告]
    R3 --> T[数据存储]
    
    T --> T1[TimescaleDB 存储]
    T1 --> T2[历史数据分析]
    T2 --> U[测试结束]
    
    style A fill:#e1f5fe
    style U fill:#c8e6c9
    style S fill:#ffcdd2
    style O fill:#fff3e0
    style R fill:#f3e5f5
```

## 流程说明

### 1. 部署方式选择

根据测试需求和环境选择合适的部署方式：

- **本地开发**: 适用于开发阶段的功能验证和小规模测试
- **容器化部署**: 适用于标准化环境和中等规模测试
- **分布式部署**: 适用于大规模负载测试和生产环境

### 2. 环境配置

#### 本地环境设置
1. 安装 uv 包管理器
2. 创建并激活虚拟环境
3. 安装项目依赖

#### Docker 部署
1. 配置环境变量文件
2. 构建自定义 Docker 镜像
3. 使用 docker-compose 启动服务

#### 分布式部署
1. 配置后端 API 服务
2. 使用 systemd 管理服务
3. 验证服务运行状态

### 3. 测试模式配置

- **单进程模式**: 使用单个 CPU 核心，适合简单测试
- **多进程模式**: 充分利用多核 CPU，提高并发能力
- **分布式模式**: 多台机器协同，支持超大规模测试

### 4. 测试场景选择

根据测试目标选择相应的测试场景：

- **Gateway**: 网关性能测试
- **Kenta**: 业务系统测试
- **Recognition**: 识别引擎测试
- **Sample**: 示例和学习场景

### 5. 测试数据准备

支持多种数据源：

- **参数化文件**: CSV/TSV 文件
- **Redis 数据**: 分布式参数管理
- **数据库数据**: 直接从数据库读取

### 6. 测试执行与监控

- **实时监控**: Web UI 提供实时性能指标
- **日志监控**: 详细的执行日志和错误信息
- **系统监控**: CPU、内存、网络等资源使用情况

### 7. 结果处理

- **报告生成**: 自动生成详细的测试报告
- **数据存储**: 使用 TimescaleDB 存储历史数据
- **趋势分析**: 支持历史数据对比和趋势分析

### 8. 故障处理

当测试过程中出现异常时：

1. 检查详细日志信息
2. 分析错误原因
3. 调整配置参数
4. 重新启动测试

## 关键节点说明

- **蓝色节点**: 流程起始点
- **绿色节点**: 流程结束点
- **红色节点**: 异常处理流程
- **橙色节点**: 监控相关流程
- **紫色节点**: 报告生成流程
