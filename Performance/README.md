# 性能测试框架

## 项目概述

本项目是基于 Locust 的性能测试框架，提供了完整的负载测试解决方案。项目采用模块化设计，支持多种测试场景和部署方式，包括单机模式、多进程模式和分布式模式。

## 项目结构

```
Performance/
└── Locust/
    ├── backend/           # 后端服务配置
    ├── env/              # 环境配置和部署
    ├── preparation/      # 测试准备工具
    └── scenarios/        # 测试场景
```

## 核心组件

### 1. 后端服务 (backend/)
- **neox-locust-backend.py**: 基于 Sanic 的后端 API 服务
- **Backend.env**: 后端服务环境配置
- **neox-locust-backend.service**: systemd 服务配置文件
- **demo_payload.json**: 示例负载数据

### 2. 环境配置 (env/)
- **Docker 环境**: 包含 Dockerfile 和 docker-compose.yml
- **依赖管理**: 使用 uv 进行包管理，支持开发和生产环境
- **本地库**: locust_common 和 neox_locust 自定义库
- **TimescaleDB**: 时序数据库支持

### 3. 测试场景 (scenarios/)
- **Gateway**: 网关测试场景
- **Kenta**: Kenta 系统测试场景
- **Recognition**: 识别引擎测试场景
- **Sample**: 示例测试场景和最佳实践

## 快速开始

### 环境准备

1. **安装 uv 包管理器**
   ```bash
   # macOS & Linux
   curl -Lfs https://astral.sh/uv/install.sh | sh
   
   # Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```

2. **设置虚拟环境**
   ```bash
   cd Performance/Locust/env/
   uv venv
   source .venv/bin/activate  # Linux/macOS
   # 或 .venv\Scripts\Activate.ps1  # Windows
   ```

3. **安装依赖**
   ```bash
   # 开发环境
   uv pip install -e .[dev]
   
   # 生产环境
   uv pip install .[latest]
   ```

### 后端服务部署

1. **配置后端服务**
   ```bash
   cd Performance/Locust/backend/
   # 编辑 Backend.env 配置文件
   ```

2. **使用 systemd 管理服务**
   ```bash
   sudo systemctl start neox-locust-backend.service
   sudo systemctl enable neox-locust-backend.service
   sudo systemctl status neox-locust-backend.service
   ```

3. **验证服务**
   ```bash
   curl -X GET http://localhost:9333/locust/config/get
   ```

### Docker 部署

1. **构建镜像**
   ```bash
   cd Performance/Locust/env/
   bash BuildImage.sh
   ```

2. **启动服务**
   ```bash
   docker-compose up -d
   ```

3. **访问 Web UI**
   ```
   http://localhost:8089
   ```

## 测试模式

### 单进程模式
适用于简单测试场景，使用单个 CPU 核心进行压测。

### 多进程模式
充分利用多核 CPU，每个 worker 使用独立进程。

### 分布式模式
支持多台机器协同进行大规模负载测试。

## 测试场景示例

### 基础 HTTP 测试
```python
from locust import HttpUser, task, between

class WebsiteUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def index_page(self):
        self.client.get("/")
```

### 参数化测试
支持从 CSV、TSV 文件或 Redis 读取测试参数。

### 自定义负载模式
支持阶梯式、峰值式等多种负载模式。

## 监控和报告

- **实时监控**: Web UI 提供实时性能指标
- **日志记录**: 详细的测试日志和错误记录
- **数据存储**: TimescaleDB 存储历史测试数据
- **报告生成**: 自动生成测试报告

## 最佳实践

1. **参数化数据管理**: 使用 Redis 进行分布式参数管理
2. **资源监控**: 监控 CPU、内存和网络使用情况
3. **渐进式压测**: 从小负载开始逐步增加
4. **错误处理**: 合理设置超时和重试机制

## 工作流程

详细的测试执行流程请参考：[测试流程图](./workflow-diagram.md)

## 故障排除

### 常见问题
1. **依赖安装失败**: 检查 Python 版本和网络连接
2. **服务启动失败**: 检查端口占用和权限设置
3. **测试数据不准确**: 验证网络延迟和系统资源

### 日志查看
```bash
# 后端服务日志
sudo tail -f /var/log/neox-locust-backend.access.log
sudo tail -f /var/log/neox-locust-backend.error.log

# Docker 容器日志
docker-compose logs -f
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系项目维护者。
