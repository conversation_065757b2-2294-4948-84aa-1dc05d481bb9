# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-12
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   test_login.py
@Software   :   PyCharm
"""

import allure
import pytest
from neox_test_scenarios import (
    perform_login_flow,
    verify_login_failure,
    verify_login_success,
)


@allure.epic("薬師丸賢太")
@allure.suite("登录")
@allure.sub_suite("登录成功")
@allure.feature("登录")
@allure.story("登录成功")
@allure.title("测试用例：登录成功")
@pytest.mark.order(after="test_login_fail")
def test_login_success(config):
    """
    TestCase: 用户登录成功
    """
    # Perform login flow with correct credentials
    perform_login_flow(config, use_correct_credentials=True)

    # Verify login success
    verify_login_success(config)


@allure.epic("薬師丸賢太")
@allure.suite("登录")
@allure.sub_suite("登录失败")
@allure.feature("登录")
@allure.story("登录失败")
@allure.title("测试用例：登录失败")
def test_login_fail(config):
    """
    TestCase: 用户登录失败
    """
    # Perform login flow with incorrect credentials
    perform_login_flow(config, use_correct_credentials=False)

    # Verify login failure
    verify_login_failure()
