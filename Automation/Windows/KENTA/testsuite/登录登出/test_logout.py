# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   test_logout.py
@Software   :   PyCharm
"""

import allure
import pytest
from neox_test_scenarios import close_application, perform_logout_flow


@allure.epic("薬師丸賢太")
@allure.suite("登出")
@allure.feature("登出")
@allure.title("测试用例：登出")
@pytest.mark.order(index=-1)
def test_logout(config):
    """
    TestCase: 用户登出
    """
    # Perform complete logout flow
    perform_logout_flow(config)

    # Close application
    close_application()
