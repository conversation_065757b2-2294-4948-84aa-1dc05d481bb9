# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_homepage_reviewed_list.py.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("阅览济列表展示（QR->QR）")
@allure.feature("首页")
@allure.story("阅览济列表展示（QR->QR）")
@allure.title("测试用例：阅览济列表展示（QR->QR）")
def test_homepage_reviewed_list_qr2qr(config):
    """
    TestCase: 首页 - 阅览济列表展示（QR->QR）
    """
    with allure.step("阅览济列表展示（QR->QR）"):
        logger.info("< Test :: 阅览济列表展示（QR->QR） >")
        # TODO: 实现具体逻辑
        assert True


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("阅览济列表展示（OCR->QR）")
@allure.feature("首页")
@allure.story("阅览济列表展示（OCR->QR）")
@allure.title("测试用例：阅览济列表展示（OCR->QR）")
def test_homepage_reviewed_list_ocr2qr(config):
    """
    TestCase: 首页 - 阅览济列表展示（OCR->QR）
    """
    with allure.step("阅览济列表展示（OCR->QR）"):
        logger.info("< Test :: 阅览济列表展示（OCR->QR） >")
        assert True


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("阅览济列表展示（QR->OCR）")
@allure.feature("首页")
@allure.story("阅览济列表展示（QR->OCR）")
@allure.title("测试用例：阅览济列表展示（QR->OCR）")
def test_homepage_reviewed_list_qr2ocr(config):
    """
    TestCase: 首页 - 阅览济列表展示（QR->OCR）
    """
    with allure.step("阅览济列表展示（QR->OCR）"):
        logger.info("< Test :: 阅览济列表展示（QR->OCR） >")
        assert True


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("阅览济列表展示（OCR->OCR）")
@allure.feature("首页")
@allure.story("阅览济列表展示（OCR->OCR）")
@allure.title("测试用例：阅览济列表展示（OCR->OCR）")
def test_homepage_reviewed_list_ocr2ocr(config):
    """
    TestCase: 首页 - 阅览济列表展示（OCR->OCR）
    """
    with allure.step("阅览济列表展示（OCR->OCR）"):
        logger.info("< Test :: 阅览济列表展示（OCR->OCR） >")
        assert True
