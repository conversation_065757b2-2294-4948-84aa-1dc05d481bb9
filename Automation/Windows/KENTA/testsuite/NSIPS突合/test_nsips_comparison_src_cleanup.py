# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_nsips_comparison_src_cleanup.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger


@allure.epic("薬師丸賢太")
@allure.suite("NSIPS突合")
@allure.sub_suite("NSIPS突合比对路径下的源数据清除验证")
@allure.feature("NSIPS突合")
@allure.story("NSIPS突合比对路径下的源数据清除验证")
@allure.title("测试用例：NSIPS突合比对路径下的源数据清除验证")
def test_nsips_comparison_src_cleanup(config):
    """
    TestCase: NSIPS突合比对路径下的源数据清除验证
    """
    with allure.step("NSIPS突合比对路径下的源数据清除验证"):
        logger.info("< Test :: NSIPS突合比对路径下的源数据清除验证 >")
        # TODO: 实现具体逻辑
        assert True
