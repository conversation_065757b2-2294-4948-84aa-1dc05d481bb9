# 自动化测试框架说明文档

本文档详细介绍了NeoX自动化测试框架的架构、组织结构和使用方法。

## 项目概述

NeoX自动化测试框架是一个基于pytest的Windows UI自动化测试解决方案。框架采用模块化设计，通过公共库提供可复用的测试组件和工具，专注于Windows应用程序的UI自动化测试。

## 整体架构

### 核心组件
- **Automation/**: 自动化测试项目主目录
- **lib/**: 公共库目录，提供可复用的测试组件
- **Windows/KENTA/**: Windows UI自动化测试项目（薬師丸賢太系统）

### 流程图
详细的测试框架流程图请参考：[自动化测试框架流程图](./automation-flow-diagram.md)

## 目录结构

### 标准项目结构
```powershell
${project_dir}
│   conftest.py          # pytest fixture配置
│   pyproject.toml      # python项目管理配置文件
│   pytest.ini           # pytest配置文件（在pyproject.toml中已包含相同配置，可选保留）
│   README.md           # 项目说明文档
│   requirements.txt    # 项目依赖
│   autorun.py          # 测试执行入口
│
├───conf               # 配置文件目录
├───testsuite         # 测试用例目录
└───report            # 测试报告输出目录
```

### 目录说明
- **conf（目录）**：存放项目运行环境和执行环境相关的配置参数
- **testsuite（目录）**：pytest测试用例实现，按功能模块分类组织
- **report（目录）**：Allure测试报告生成目录
- **conftest.py（文件）**：pytest的fixture方法定义，测试用例可直接使用无需import
- **pyproject.toml（文件）**：python项目管理的配置文件，包含镜像源、Python版本等设置
- **pytest.ini（文件）**：pytest框架配置，包括日志格式和级别设置
- **runall.py（文件）**：测试执行入口，主要用于CI/CD工具调用
- **requirements.txt（文件）**：项目依赖的Python第三方库列表

**注意：** 以上为标准项目结构，可根据实际需求调整，公共功能已抽取到lib目录作为外部包

## 公共库架构

### lib目录结构
```
lib/
├── neox_test_common/      # 通用测试工具库
├── neox_test_win/         # Windows自动化测试库
└── neox_test_scenarios/   # 测试场景库
```

### 库功能说明

#### neox_test_common
- **功能**：提供通用的测试工具和基础组件
- **主要模块**：
  - `common/`: 全局上下文管理、UI自动化基础类
  - `log/`: 基于loguru的日志管理系统
- **用途**：配置管理、日志记录、UI自动化基础功能

#### neox_test_win
- **功能**：Windows平台UI自动化测试支持
- **主要模块**：
  - `yakushi/`: Yakushi应用专用自动化组件
  - `config.py`: 特性文件路径配置
  - `screen.py`: 屏幕操作和截图功能
- **用途**：Windows应用控制、UI元素交互、屏幕操作

#### neox_test_scenarios
- **功能**：测试场景的具体实现
- **主要模块**：
  - `yakushi/login_logout/`: 登录登出场景
  - `yakushi/homepage/`: 首页功能场景
  - `yakushi/prescription_history/`: 处方笺履历场景
  - `yakushi/nsips_comparison/`: NSIPS突合场景
  - `yakushi/setting/`: 设定功能场景
  - `yakushi/ui/`: UI验证场景
- **用途**：业务流程自动化、功能测试场景实现

## Windows/KENTA项目详解

### 项目概述
KENTA项目是针对薬師丸賢太系统的Windows UI自动化测试项目。

### 项目结构
```
Windows/KENTA/
├── conf/
│   └── yakushi.toml       # Yakushi应用配置
├── testsuite/            # 测试用例实现
│   ├── 登录登出/
│   ├── 首页/
│   ├── 处方笺履历/
│   ├── NSIPS突合/
│   ├── 设定/
│   ├── UI/
│   └── 其他/
├── conftest.py           # pytest配置和fixture
├── requirements.txt      # 项目依赖
└── runall.py            # 测试执行入口
```

### 测试模块说明

#### 登录登出模块
- **功能**：用户身份验证流程测试
- **测试场景**：登录成功、登录失败、登出流程
- **配置**：账号密码在yakushi.toml中配置

#### 首页模块
- **功能**：主界面功能测试
- **测试场景**：已阅览/未阅览列表展示、QR/OCR模式切换

#### 处方笺履历模块
- **功能**：处方历史记录管理
- **测试场景**：详情查看、故障处理、筛选功能、分页功能

#### NSIPS突合模块
- **功能**：NSIPS数据比对功能
- **测试场景**：数据清理、详情查看、筛选、分页、排序、更新

#### 设定模块
- **功能**：系统配置管理
- **测试场景**：路径配置、QR配置、PDF通知配置、输入文件配置

#### UI模块
- **功能**：用户界面验证
- **测试场景**：浮动窗口、主页窗口、弹出窗口验证

### 配置文件模板

#### pyproject.toml（推荐）
现代化的项目配置文件，包含项目元数据、依赖管理和工具配置：

```toml
[project]
name = "kenta-automation"
version = "1.0.0"
requires-python = ">=3.11"
dependencies = [
    "pytest==8.3.5",
    # ... 其他依赖
]

[tool.pytest.ini_options]
log_cli = true
log_cli_level = "INFO"
testpaths = ["Windows/KENTA/testsuite"]

[tool.uv.sources]
neox_test_common = { path = "../lib/neox_test_common", editable = true }
# ... 其他本地包
```

#### pytest.ini（传统方式）
日志格式和级别配置，支持控制台输出和文件输出：

```ini
[pytest]
log_cli = 1
log_cli_level = INFO
log_cli_date_format = %Y-%m-%d-%H-%M-%S
log_cli_format = %(asctime)s - %(filename)s - %(module)s - %(funcName)s - %(lineno)d - %(levelname)s - %(message)s
log_file = ./log/run.log
log_file_level = INFO
log_file_date_format = %Y-%m-%d-%H-%M-%S
log_file_format = %(asctime)s - %(filename)s - %(module)s - %(funcName)s - %(lineno)d - %(levelname)s - %(message)s
```

#### 项目依赖（pyproject.toml 管理）
所有依赖现已整合到 `pyproject.toml` 文件中，包括：

**测试框架依赖**：
- pytest 系列：核心测试框架及插件
- allure-pytest：测试报告生成
- 工具库：pip-autoremove, pipdeptree

**Windows UI自动化依赖**：
- playwright：Web UI自动化
- PyAutoGUI：桌面UI自动化
- uiautomation：Windows UI自动化
- pillow：图像处理
- jmespath：JSON数据处理

**本地可编辑包**：
- neox_test_common：通用测试工具库
- neox_test_win：Windows自动化测试库
- neox_test_scenarios：测试场景库

#### 传统依赖文件（已弃用）
以下文件仅作参考，新项目请使用 `pyproject.toml`：

**公共依赖 (requirements_auto_common.txt)**：
```bash
pytest==8.3.5
pytest-html==4.1.1
# ... 其他依赖
```

**KENTA项目依赖 (Windows/KENTA/requirements.txt)**：
```bash
jmespath==1.0.1
playwright==1.52.0
# ... 其他依赖
-e ../../../lib/neox_test_common
```

## 项目依赖管理变更说明

### 重要变更
从版本 1.0.0 开始，本项目已从传统的 `requirements.txt` 文件迁移到现代的 `pyproject.toml` 配置文件。这一变更带来以下优势：

- **统一配置管理**：项目元数据、依赖、工具配置统一在一个文件中
- **更好的依赖解析**：支持更精确的版本约束和依赖关系管理
- **现代包管理工具支持**：完全兼容 `uv`、`pip`、`poetry` 等现代包管理工具
- **标准化**：符合 PEP 518 和 PEP 621 标准，与 Python 生态系统保持一致

### 迁移内容
- `requirements_auto_common.txt` → `pyproject.toml` 的 `dependencies` 部分
- `Windows/KENTA/requirements.txt` → `pyproject.toml` 的 `optional-dependencies` 部分
- 可编辑包（`-e` 指向的本地库）→ `tool.uv.sources` 配置

## 环境管理指南

### 方式一：使用 uv（推荐）

`uv` 是一个快速的 Python 包管理器，提供了优秀的依赖解析和虚拟环境管理功能。

#### uv 配置文件 (pyproject.toml)
项目根目录下的 `pyproject.toml` 文件提供了 uv 工具的全局配置：

```toml
[tool.uv.pip]
# 使用阿里云镜像源加速包下载
index-url = "https://mirrors.aliyun.com/pypi/simple"

# 指定最低 Python 版本要求
python-version = "3.11"

# 指定项目使用的 Python 解释器路径（根据自己本地的python解释器所在目录配置对应路径）
python = "C:\\miniconda3\\envs\\python3.13"
```

**配置说明**：
- `index-url`：设置 PyPI 镜像源，使用阿里云镜像可显著提升国内用户的包下载速度
- `python-version`：指定项目支持的最低 Python 版本
- `python`：指定 uv 创建虚拟环境时使用的 Python 解释器路径

#### 1. 安装 uv
```bash
# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或者使用 pip 安装
pip install uv
```

#### 2. 创建虚拟环境
```bash
# 在 Automation 目录下执行
cd .\\Automation\\KENTA
uv venv
```

#### 3. 激活虚拟环境
```bash
# Windows
.venv\Scripts\activate
```

#### 4. 安装项目依赖
```bash
# 安装所有依赖（包括本地可编辑包）
uv sync --all-extras

# 或者手动安装
uv pip install -e .

# 安装所有 optional-dependencies 的包
uv pip install -e .[dev]

# 也可以指定只同步 kenta 相关的包和本地包
uv sync --extra kenta --extra local
# 等价于
uv pip install -e .[kenta,local]
```

#### 5. 运行测试
```bash
# 使用 uv 运行测试
uv run pytest Windows/KENTA/testsuite/登录登出/test_login.py -v

# 或者激活环境后直接运行
pytest Windows/KENTA/testsuite/登录登出/test_login.py -v
```

### 方式二：使用 conda（备选方案）

如果您更熟悉 conda 环境管理，也可以使用以下方式：

#### 1. 创建 conda 环境
```bash
# 创建新环境
conda create -n neox-automation python=3.13

# 激活环境
conda activate neox-automation
```

#### 2. 安装依赖
```bash
# 进入 Automation 目录
cd Automation

# 使用 pip 安装项目依赖
pip install -e .

# 或者分别安装依赖文件（传统方式）
pip install -r requirements_auto_common.txt
pip install -r Windows/KENTA/requirements.txt
```

#### 3. 验证安装
```bash
# 检查已安装的包
pip list

# 运行简单测试验证环境
python -c "import pytest; import playwright; print('Environment setup successful!')"
```

### 环境验证

无论使用哪种方式，都可以通过以下命令验证环境设置是否正确：

```bash
# 检查 Python 版本
python --version

# 检查关键依赖
python -c "import pytest, playwright, neox_test_common; print('All dependencies imported successfully!')"

# 运行依赖检查
pipdeptree

# 运行简单测试
pytest --version
```

## 测试执行方式

### 命令行执行
```bash
# 进入项目目录
cd Automation\Windows\KENTA

# 查看命令行帮助文档
uv run python autorun.py -h

# 执行所有测试
uv run python autorun.py --all
```

### 单个测试执行
```python
# 执行登录测试
pytest.main(["-vs", "./testsuite/登录登出/test_login.py"])

# 执行特定测试方法
pytest.main(["-vs", "./testsuite/登录登出/test_login.py::test_login_success"])
```

### 模块测试执行
```python
# 执行整个登录登出模块
pytest.main(['-vs', './testsuite/登录登出'])

# 带重试和失败停止的执行
pytest.main(['-vs', './testsuite/登录登出', '--reruns=2', '--reruns-delay=3', '--maxfail=1'])
```

### CI/CD集成
runall.py文件提供了CI/CD工具集成接口：

```python
import pytest

if __name__ == "__main__":
    # 基础执行
    pytest.main(["-vs", "./testsuite/登录登出/test_login.py"])

    # 生成Allure报告
    # os.system("allure generate ./temp -o ./report --clean")
    # os.system("allure open ./report")
```

## 特性文件与测试用例映射

### 测试用例开发流程
1. **场景实现**：在neox_test_scenarios库中实现具体的测试步骤
2. **测试用例**：在testsuite目录下编写pytest测试用例，调用场景实现

### 示例映射关系
```
neox_test_scenarios/yakushi/login_logout/
    ↓ (测试调用)
testsuite/登录登出/test_login.py
```

## 测试报告

### Allure报告
- **配置**：通过conftest.py中的pytest_runtest_makereport钩子实现
- **截图**：测试失败时自动截图并附加到报告
- **分层**：使用@allure装饰器进行epic、suite、feature、story分层

### 报告结构
```python
@allure.epic("薬師丸賢太")
@allure.suite("登录")
@allure.sub_suite("登录成功")
@allure.feature("登录")
@allure.story("登录成功")
@allure.title("测试用例：登录成功")
```

## 最佳实践

### 测试用例编写
1. **使用Page Object模式**：UI元素封装
2. **数据驱动测试**：配置文件驱动
3. **异常处理**：失败截图和日志记录

### 配置管理
1. **环境隔离**：不同环境使用不同配置文件
2. **敏感信息**：账号密码等敏感信息统一配置
3. **参数化**：支持运行时参数调整

### 维护建议
1. **定期更新依赖**：保持库版本更新
2. **代码复用**：充分利用公共库
3. **文档同步**：及时更新文档和注释
4. **持续集成**：集成到CI/CD流水线

## 故障排除

### 常见问题
1. **应用启动失败**：检查yakushi.toml中的exe_abs_path配置
2. **UI元素定位失败**：检查auto_id和class_name配置
3. **测试超时**：调整timeout配置参数
4. **依赖冲突**：使用pip-autoremove清理无用依赖

### 调试技巧
1. **日志分析**：查看./log/run.log日志文件
2. **截图分析**：失败时的自动截图
3. **分步执行**：使用-vs参数详细输出
4. **断点调试**：IDE中设置断点调试

## 扩展开发

### 添加新测试模块
1. 在neox_test_scenarios中实现场景步骤
2. 在testsuite中编写pytest测试用例
3. 更新配置文件和依赖

### 自定义库开发
1. 在lib目录下创建新的库项目
2. 使用pyproject.toml管理项目配置
3. 通过-e参数安装为可编辑包
4. 在测试项目中引用使用

---

**注意**：本框架持续更新中，使用过程中如遇问题请及时反馈和更新文档。
