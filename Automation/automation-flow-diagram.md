# 自动化测试框架流程图

```mermaid
graph TD
    A[项目启动] --> B[加载配置文件]
    B --> C[KENTA项目]

    C --> D[读取yakushi.toml配置]
    D --> E[初始化测试环境]
    E --> F[启动Yakushi应用]
    F --> G[执行测试用例]

    G --> H{测试模块}
    H -->|登录登出| I[authentication.feature]
    H -->|首页功能| J[homepage features]
    H -->|处方笺履历| K[prescription history]
    H -->|NSIPS突合| L[NSIPS comparison]
    H -->|设定功能| M[settings features]
    H -->|UI验证| N[UI validation]
    H -->|其他功能| O[other features]

    I --> P[执行BDD场景]
    J --> P
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P

    P --> Q[调用lib库函数]
    Q --> R{使用的库}
    R -->|通用功能| S[neox_test_common]
    R -->|Windows自动化| T[neox_test_win]
    R -->|BDD场景| U[neox_test_scenarios]

    S --> V[日志记录<br/>配置管理<br/>UI自动化基础]
    T --> W[Yakushi应用控制<br/>屏幕操作<br/>Windows UI交互]
    U --> X[登录登出场景<br/>业务流程场景<br/>UI验证场景]

    V --> Y[执行测试步骤]
    W --> Y
    X --> Y

    Y --> Z{测试结果}
    Z -->|成功| AA[生成测试报告]
    Z -->|失败| BB[截图保存]

    BB --> CC[Allure报告]
    AA --> CC

    CC --> DD[测试完成]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style CC fill:#e8f5e8
    style DD fill:#ffebee
```

## 流程说明

### 主要流程
1. **项目启动**：初始化测试环境
2. **配置加载**：读取yakushi.toml配置文件
3. **KENTA项目**：专门针对薬師丸賢太系统的Windows UI自动化测试
4. **测试执行**：按功能模块执行自动化测试
5. **结果处理**：生成Allure报告和处理异常

### Windows UI自动化测试流程
1. **KENTA项目**：专门针对薬師丸賢太系统
2. **配置读取**：yakushi.toml配置文件
3. **环境初始化**：准备测试环境
4. **应用启动**：启动Yakushi被测应用
5. **模块测试**：按功能模块执行测试
   - 登录登出模块
   - 首页功能模块
   - 处方笺履历模块
   - NSIPS突合模块
   - 设定功能模块
   - UI验证模块
   - 其他功能模块
6. **BDD场景**：执行具体的业务场景
7. **库调用**：使用公共库实现测试逻辑

### 公共库架构
- **neox_test_common**：通用功能库（日志记录、配置管理、UI自动化基础）
- **neox_test_win**：Windows自动化库（Yakushi应用控制、屏幕操作、Windows UI交互）
- **neox_test_scenarios**：BDD场景库（登录登出场景、业务流程场景、UI验证场景）

### 测试结果处理
1. **成功场景**：直接生成测试报告
2. **失败场景**：自动截图保存，附加到Allure报告
3. **报告生成**：统一生成Allure格式的测试报告
4. **测试完成**：输出最终测试结果
