# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-26
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   GlobalContext.py
@Software   :   PyCharm
"""


# 1，在 __init__ 中 from neox_test_common.common import GlobalContext as gbc；
# 2，在 __init__ 中调用 __init 方法初始化；
# 3，在其他 *.py 文件中 from neox_test_common.common import GlobalContext as gbc；
# 4，调用 gbc 下对应方法 (show_all/add_kv 等)，即可实现跨模块全局变量的增删改查。

def __init(global_data):
    """
    初始化全局变量
    :param global_data: 初始化参数 k=v
    :return:
    """
    global _global_data
    _global_data = global_data if global_data else {}


def show_all(default=None):
    """
    获取全部
    :param default: None
    :return:
    """
    try:
        return _global_data
    except Exception:
        return default


def add_kv(key, value):
    """
    设置
    :param key: Key
    :param value: Value
    :return:
    """
    _global_data[key] = value


def get_value(key, default=None):
    """
    获取单个 key 的值
    :param key: Key
    :param default: None
    :return:
    """
    try:
        return _global_data[key]
    except KeyError:
        return default


def rm_key(key, default=None):
    """
    删除单个key
    :param key: Key
    :param default: None
    :return:
    """
    try:
        return _global_data.pop(key)
    except KeyError:
        return default


def clear_all():
    """清空"""
    _global_data.clear()
