#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   common.py
@Software   :   PyCharm
"""

import json
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Mapping, MutableMapping, Union

import toml
import yaml

# Constants
DEFAULT_ENCODING = "utf-8"
YAML_FLOW_STYLE = False
JSON_INDENT = 2


def load_toml(
    config_file: Path,
) -> Union[MutableMapping[str, Any], List[Dict[str, Any]]]:
    """
    Load a TOML configuration file and return the data as a Python object.

    This function reads a TOML file from the specified path and parses it into
    a Python data structure. It handles both dictionary-style and list-style
    TOML configurations.

    Args:
        config_file: Path to the TOML configuration file to load

    Returns:
        Parsed TOML data as either a mutable mapping or list of dictionaries.
        The exact type depends on the structure of the TOML file.

    Raises:
        FileNotFoundError: If the specified TOML file does not exist
        toml.TomlDecodeError: If the TOML file contains invalid syntax
        PermissionError: If there are insufficient permissions to read the file

    Example:
        >>> from pathlib import Path
        >>> config_path = Path("config/app.toml")
        >>> config_data = load_toml(config_path)
        >>> print(config_data.get("database", {}).get("host"))
        localhost
    """
    try:
        # 打开并读取 TOML 配置文件
        with open(config_file, "r", encoding=DEFAULT_ENCODING) as f:
            config = toml.load(f)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"TOML configuration file not found: {config_file}")
    except toml.TomlDecodeError as e:
        raise toml.TomlDecodeError(f"Invalid TOML syntax in {config_file}: {str(e)}")
    except PermissionError:
        raise PermissionError(f"Permission denied reading TOML file: {config_file}")


def dump_toml(
    config_file: Path,
    object_dict: Union[Mapping[Any, Any], List[Mapping[str, Any]]],
) -> Path:
    """
    Write a Python object to a TOML configuration file.

    This function serializes a Python data structure (dictionary or list of
    dictionaries) to TOML format and writes it to the specified file path.

    Args:
        config_file: Path where the TOML file will be written
        object_dict: Data to serialize and write to the TOML file

    Returns:
        Path to the successfully written TOML file

    Raises:
        PermissionError: If there are insufficient permissions to write the file
        OSError: If there are filesystem-related errors during writing
        TypeError: If the object_dict contains non-serializable data types

    Example:
        >>> from pathlib import Path
        >>> config_data = {"database": {"host": "localhost", "port": 5432}}
        >>> output_path = dump_toml(Path("config/app.toml"), config_data)
        >>> print(f"Configuration saved to: {output_path}")
        Configuration saved to: config/app.toml
    """
    try:
        # 将 Python 对象序列化为 TOML 格式并写入文件
        with open(config_file, "w", encoding=DEFAULT_ENCODING) as f:
            toml.dump(object_dict, f)
        return config_file
    except PermissionError:
        raise PermissionError(f"Permission denied writing TOML file: {config_file}")
    except OSError as e:
        raise OSError(f"Filesystem error writing TOML file {config_file}: {str(e)}")
    except TypeError as e:
        raise TypeError(f"Cannot serialize object to TOML format: {str(e)}")


def load_yml(config_file: Path) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Load a YAML configuration file and return the data as a Python object.

    This function reads a YAML file from the specified path and parses it into
    a Python data structure. It supports both dictionary-style and list-style
    YAML configurations using the safe FullLoader.

    Args:
        config_file: Path to the YAML configuration file to load

    Returns:
        Parsed YAML data as either a dictionary or list of dictionaries.
        The exact type depends on the structure of the YAML file.

    Raises:
        FileNotFoundError: If the specified YAML file does not exist
        yaml.YAMLError: If the YAML file contains invalid syntax
        PermissionError: If there are insufficient permissions to read the file

    Example:
        >>> from pathlib import Path
        >>> config_path = Path("config/app.yaml")
        >>> config_data = load_yml(config_path)
        >>> print(config_data.get("server", {}).get("port"))
        8080
    """
    try:
        # 打开并读取 YAML 配置文件
        with open(config_file, "r", encoding=DEFAULT_ENCODING) as f:
            config = yaml.load(f.read(), Loader=yaml.FullLoader)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"YAML configuration file not found: {config_file}")
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Invalid YAML syntax in {config_file}: {str(e)}")
    except PermissionError:
        raise PermissionError(f"Permission denied reading YAML file: {config_file}")


def dump_yml(
    config_file: Path,
    object_dict: Union[Mapping[Any, Any], List[Mapping[str, Any]]],
) -> Path:
    """
    Write a Python object to a YAML configuration file.

    This function serializes a Python data structure (dictionary or list of
    dictionaries) to YAML format and writes it to the specified file path.
    The output uses human-readable formatting with Unicode support.

    Args:
        config_file: Path where the YAML file will be written
        object_dict: Data to serialize and write to the YAML file

    Returns:
        Path to the successfully written YAML file

    Raises:
        PermissionError: If there are insufficient permissions to write the file
        OSError: If there are filesystem-related errors during writing
        yaml.YAMLError: If the object contains non-serializable data types

    Example:
        >>> from pathlib import Path
        >>> config_data = {"server": {"host": "localhost", "port": 8080}}
        >>> output_path = dump_yml(Path("config/app.yaml"), config_data)
        >>> print(f"Configuration saved to: {output_path}")
        Configuration saved to: config/app.yaml
    """
    try:
        # 将 Python 对象序列化为 YAML 格式并写入文件
        with open(config_file, "w", encoding=DEFAULT_ENCODING) as f:
            yaml.dump(
                object_dict,
                f,
                default_flow_style=YAML_FLOW_STYLE,
                encoding=DEFAULT_ENCODING,
                allow_unicode=True,
            )
        return config_file
    except PermissionError:
        raise PermissionError(f"Permission denied writing YAML file: {config_file}")
    except OSError as e:
        raise OSError(f"Filesystem error writing YAML file {config_file}: {str(e)}")
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Cannot serialize object to YAML format: {str(e)}")


def load_json_file(json_file: Path) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Load a JSON configuration file and return the data as a Python object.

    This function reads a JSON file from the specified path and parses it into
    a Python data structure. It supports both dictionary-style and list-style
    JSON configurations.

    Args:
        json_file: Path to the JSON configuration file to load

    Returns:
        Parsed JSON data as either a dictionary or list of dictionaries.
        The exact type depends on the structure of the JSON file.

    Raises:
        FileNotFoundError: If the specified JSON file does not exist
        json.JSONDecodeError: If the JSON file contains invalid syntax
        PermissionError: If there are insufficient permissions to read the file

    Example:
        >>> from pathlib import Path
        >>> config_path = Path("config/app.json")
        >>> config_data = load_json_file(config_path)
        >>> print(config_data.get("api", {}).get("version"))
        v1
    """
    try:
        # 打开并读取 JSON 配置文件
        with open(json_file, "r", encoding=DEFAULT_ENCODING) as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        raise FileNotFoundError(f"JSON configuration file not found: {json_file}")
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Invalid JSON syntax in {json_file}: {str(e)}")
    except PermissionError:
        raise PermissionError(f"Permission denied reading JSON file: {json_file}")


def dump_json_file(
    json_file: Path,
    object_dict: Union[Mapping[Any, Any], List[Mapping[str, Any]]],
) -> Path:
    """
    Write a Python object to a JSON configuration file.

    This function serializes a Python data structure (dictionary or list of
    dictionaries) to JSON format and writes it to the specified file path.
    The output uses pretty-printing with Unicode support.

    Args:
        json_file: Path where the JSON file will be written
        object_dict: Data to serialize and write to the JSON file

    Returns:
        Path to the successfully written JSON file

    Raises:
        PermissionError: If there are insufficient permissions to write the file
        OSError: If there are filesystem-related errors during writing
        TypeError: If the object contains non-serializable data types

    Example:
        >>> from pathlib import Path
        >>> config_data = {"api": {"version": "v1", "timeout": 30}}
        >>> output_path = dump_json_file(Path("config/app.json"), config_data)
        >>> print(f"Configuration saved to: {output_path}")
        Configuration saved to: config/app.json
    """
    try:
        # 将 Python 对象序列化为 JSON 格式并写入文件
        with open(json_file, "w", encoding=DEFAULT_ENCODING) as jf:
            jf.write(json.dumps(object_dict, indent=JSON_INDENT, ensure_ascii=False))
        return json_file
    except PermissionError:
        raise PermissionError(f"Permission denied writing JSON file: {json_file}")
    except OSError as e:
        raise OSError(f"Filesystem error writing JSON file {json_file}: {str(e)}")
    except TypeError as e:
        raise TypeError(f"Cannot serialize object to JSON format: {str(e)}")


@dataclass(frozen=False)
class ProjectConfig:
    """
    Configuration manager for NeoX project settings.

    This class provides a convenient interface for loading and accessing
    project configuration data from TOML files. It supports customizable
    configuration file paths and automatic loading of configuration data.

    Attributes:
        conf_path: List of directory names and filename components that form
                  the path to the configuration file relative to the current
                  working directory

    Example:
        >>> config = ProjectConfig()
        >>> data = config.conf_data()
        >>> print(data.get("database", {}).get("host"))
        localhost

        >>> # Custom configuration path
        >>> custom_config = ProjectConfig(conf_path=["config", "custom.toml"])
        >>> custom_data = custom_config.conf_data()
    """

    conf_path: List[str] = field(default_factory=lambda: ["conf", "neox.toml"])

    def conf_data(self) -> Union[MutableMapping[str, Any], List[Dict[str, Any]]]:
        """
        Load and return the configuration data from the specified TOML file.

        This method constructs the full path to the configuration file using
        the current working directory and the configured path components,
        then loads and returns the parsed TOML data.

        Returns:
            Parsed configuration data from the TOML file. The structure
            depends on the content of the configuration file.

        Raises:
            FileNotFoundError: If the configuration file does not exist
            toml.TomlDecodeError: If the configuration file contains invalid TOML syntax
            PermissionError: If there are insufficient permissions to read the file

        Example:
            >>> config = ProjectConfig()
            >>> data = config.conf_data()
            >>> database_config = data.get("database", {})
            >>> host = database_config.get("host", "localhost")
        """
        # 构建配置文件的完整路径
        config_file_path = Path.cwd().joinpath(*self.conf_path)
        return load_toml(config_file_path)
