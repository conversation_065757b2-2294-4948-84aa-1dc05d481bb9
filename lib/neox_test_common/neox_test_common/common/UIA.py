#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-29
<AUTHOR>   Kuno<PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   UIA.py
@Software   :   PyCharm
"""

import os
import subprocess
from pathlib import Path
from typing import Optional, Union

import pendulum
import uiautomation as ui

# Constants
DEFAULT_SEARCH_DEPTH = 0xFFFFFFFF
DEFAULT_TIMEOUT = 5
DEFAULT_WAIT_TIME = 0.5
TIME_FORMAT = "YYYY-MM-DD_HH-mm-ss"
LOG_FILE_PREFIX = "UIA-Log_"
LOG_FILE_EXTENSION = ".txt"


class UIA:
    """
    Windows 应用程序测试的 UI 自动化封装类。

    该类提供了一套全面的静态方法，用于使用 UI 自动化框架自动化 Windows 应用程序。
    包括窗口管理、控件交互、日志记录和各种 UI 操作功能。

    该类作为 uiautomation 库的高级接口，为常见的自动化任务提供便捷的方法，
    同时处理错误情况并提供一致的行为。
    """

    @staticmethod
    def setLogPath(save_path: Union[str, Path]) -> None:
        """
        配置 UI 自动化操作的日志文件路径。

        该方法通过在指定目录中创建带时间戳的日志文件来设置 UI 自动化活动的日志记录。
        如果目录不存在，将自动创建。

        参数:
            save_path: 日志文件将要创建的目录路径，可以是字符串或 Path 对象

        示例:
            >>> UIA.setLogPath("/var/log/automation")
            >>> UIA.setLogPath(Path("logs/ui_tests"))

        注意:
            日志文件将按以下模式命名：
            UIA-Log_YYYY-MM-DD_HH-mm-ss.txt
        """
        # 确保日志目录存在
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)

        # 生成带时间戳的日志文件名
        timestamp = pendulum.now(tz=pendulum.local_timezone()).format(TIME_FORMAT)
        filename = f"{LOG_FILE_PREFIX}{timestamp}{LOG_FILE_EXTENSION}"

        # 设置 UI Automation 日志文件路径
        log_file_path = save_path / filename
        ui.Logger.SetLogFile(str(log_file_path))

    @staticmethod
    def openApplication(app: Union[str, Path]) -> bool:
        """
        通过可执行文件路径或应用程序名称启动应用程序。

        该方法使用可执行文件路径或应用程序名称启动应用程序。对于非系统应用程序，
        建议使用可执行文件的绝对路径以获得更好的可靠性。

        参数:
            app: 应用程序名称或可执行文件的绝对路径，可以是字符串或 Path 对象

        返回:
            如果应用程序成功启动则返回 True，如果可执行文件不存在（对于绝对路径）则返回 False

        异常:
            subprocess.SubprocessError: 启动进程时出现问题
            OSError: 无法找到或执行可执行文件

        示例:
            >>> # 使用绝对路径启动（推荐）
            >>> success = UIA.openApplication(r"C:\\Program Files\\MyApp\\app.exe")
            >>>
            >>> # 使用应用程序名称启动（适用于系统应用）
            >>> success = UIA.openApplication("notepad.exe")

        注意:
            对于绝对路径，该方法在尝试启动前会验证文件是否存在。
            工作目录将设置为包含可执行文件的目录。
        """
        app_path = Path(app) if isinstance(app, str) else app

        # 检查绝对路径的可执行文件是否存在
        if app_path.is_absolute():
            if not app_path.exists():
                return False
            # 设置工作目录为可执行文件所在目录
            working_dir = app_path.parent
        else:
            # 对于相对路径或应用名称，使用当前目录
            working_dir = Path.cwd()

        try:
            # 启动应用程序进程
            subprocess.Popen(str(app_path), cwd=str(working_dir))
            return True
        except (subprocess.SubprocessError, OSError):
            # 启动失败时返回 False
            return False

    @staticmethod
    def closeApplication(exe_name: str) -> bool:
        """
        通过可执行文件名强制关闭应用程序。

        该方法对具有指定可执行文件名的所有进程执行系统级强制终止。
        应谨慎使用，因为它会强制终止进程，不允许它们保存数据或执行清理操作。

        参数:
            exe_name: 可执行文件名称（例如："notepad.exe"）

        返回:
            始终返回 True。注意这不保证应用程序实际在运行或成功终止。

        警告:
            这是强制终止，可能导致数据丢失。
            仅在正常应用程序关闭方法失败时使用。

        示例:
            >>> # 强制关闭所有记事本实例
            >>> UIA.closeApplication("notepad.exe")
            >>>
            >>> # 强制关闭自定义应用程序
            >>> UIA.closeApplication("myapp.exe")

        注意:
            该方法使用 Windows taskkill 命令的 /F（强制）和 /IM（映像名称）标志来终止进程。
        """
        try:
            # 使用 taskkill 命令强制终止指定名称的所有进程
            os.popen(f"taskkill /F /IM {exe_name}")
            return True
        except Exception:
            # 即使出现异常也返回 True，保持原有行为
            return True

    @staticmethod
    def WindowControl(
        Name: Optional[str],
        ClassName: Optional[str] = None,
        AutomationId: Optional[str] = None,
        searchDepth: int = DEFAULT_SEARCH_DEPTH,
        timeout: int = DEFAULT_TIMEOUT,
    ) -> Optional[ui.WindowControl]:
        """
        根据指定属性查找并返回窗口控件对象。

        该方法使用各种标识属性（如 Name、ClassName 和 AutomationId）搜索窗口控件。
        它会在指定的超时时间内等待窗口存在。

        参数:
            Name: 窗口名称属性值（必需）
            ClassName: 窗口类名属性值（可选）
            AutomationId: 窗口自动化 ID 属性值（可选）
            searchDepth: UI 树中的最大搜索深度
            timeout: 等待窗口存在的最大时间（秒）

        返回:
            如果在超时时间内找到则返回 WindowControl 对象，否则返回 None

        示例:
            >>> # 仅通过名称查找窗口
            >>> window = UIA.WindowControl("Calculator")
            >>>
            >>> # 使用多个属性查找窗口
            >>> window = UIA.WindowControl(
            ...     Name="MyApp",
            ...     ClassName="WindowClass",
            ...     AutomationId="MainWindow"
            ... )
            >>>
            >>> if window:
            ...     print(f"Found window: {window.Name}")

        注意:
            该方法根据提供的参数动态构建搜索属性，忽略 None 值以允许灵活搜索。
        """
        # 构建搜索属性字典，忽略 None 值
        search_properties = {}
        if Name is not None:
            search_properties["Name"] = Name
        if ClassName is not None:
            search_properties["ClassName"] = ClassName
        if AutomationId is not None:
            search_properties["AutomationId"] = AutomationId

        # 创建窗口控件对象
        window = ui.WindowControl(searchDepth=searchDepth, **search_properties)

        # 等待窗口存在并返回结果
        if ui.WaitForExist(window, timeout):
            return window
        return None

    @staticmethod
    def setWindowActive(window: ui.WindowControl) -> bool:
        """
        激活指定窗口并将其置于前台。

        该方法将指定窗口设置为活动窗口，将其置于前台并给予键盘焦点。

        参数:
            window: 要激活的窗口控件对象

        返回:
            如果窗口成功激活则返回 True，否则返回 False

        示例:
            >>> window = UIA.WindowControl("Calculator")
            >>> if window:
            ...     success = UIA.setWindowActive(window)
            ...     print(f"Window activated: {success}")
        """
        return window.SetActive()

    @staticmethod
    def setWindowTopMost(window: ui.WindowControl) -> bool:
        """
        设置指定窗口始终保持在其他窗口之上。

        该方法配置窗口使其保持在所有其他窗口之上可见，即使失去焦点时也是如此。

        参数:
            window: 要设置为置顶的窗口控件对象

        返回:
            如果窗口成功设置为置顶则返回 True，否则返回 False

        示例:
            >>> window = UIA.WindowControl("Important Dialog")
            >>> if window:
            ...     success = UIA.setWindowTopMost(window)
            ...     print(f"Window set topmost: {success}")
        """
        return window.SetTopmost()

    @staticmethod
    def moveWindowToCenter(window: ui.WindowControl) -> bool:
        """
        将指定窗口移动到屏幕中央。

        该方法重新定位窗口，使其在当前显示器上居中显示。

        参数:
            window: 要居中的窗口控件对象

        返回:
            如果窗口成功居中则返回 True，否则返回 False

        示例:
            >>> window = UIA.WindowControl("MyApp")
            >>> if window:
            ...     success = UIA.moveWindowToCenter(window)
            ...     print(f"Window centered: {success}")
        """
        return window.MoveToCenter()

    @staticmethod
    def checkWindowExist(window: ui.WindowControl, timeout: int) -> bool:
        """
        检查指定窗口是否在超时时间内存在。

        参数:
            window: 要检查的窗口控件对象
            timeout: 等待窗口存在的最大时间（秒）

        返回:
            如果窗口在超时时间内存在则返回 True，否则返回 False
        """
        return window.Exists(timeout)

    @staticmethod
    def closeWindow(window: ui.WindowControl) -> bool:
        """
        使用窗口模式关闭指定窗口。

        参数:
            window: 要关闭的窗口控件对象

        返回:
            如果窗口成功关闭则返回 True，否则返回 False
        """
        pattern = window.GetWindowPattern()
        if pattern is None:
            return False
        return pattern.Close()

    @staticmethod
    def showWindowMax(window: ui.WindowControl) -> bool:
        """
        最大化指定窗口。

        参数:
            window: 要最大化的窗口控件对象

        返回:
            如果窗口成功最大化则返回 True，否则返回 False
        """
        if window.IsMaximize():
            return True
        return window.Maximize()

    @staticmethod
    def showWindowMin(window: ui.WindowControl) -> bool:
        """
        最小化指定窗口。

        参数:
            window: 要最小化的窗口控件对象

        返回:
            如果窗口成功最小化则返回 True，否则返回 False
        """
        if window.IsMinimize():
            return True
        return window.Minimize()

    @staticmethod
    def switchWindow(window: ui.WindowControl) -> bool:
        """
        切换到目标窗口。

        参数:
            window: 目标窗口对象

        返回:
            如果成功切换到窗口则返回 True，否则返回 False
        """
        if not window.IsTopLevel():
            return False
        window.SwitchToThisWindow()
        return True

    @staticmethod
    def MenuItemControl(parent: ui.WindowControl, SubName=None, Name=None):
        """
        获取菜单选项控件。

        参数:
            parent: 父窗体
            SubName: 控件部分名字
            Name: 控件名字，SubName和Name只能使用一个，不能同时使用

        返回:
            菜单项控件对象

        注意:
            有时候通过Name可能获取不到控件，可以尝试通过SubName获取
        """
        if SubName is not None:
            return parent.MenuItemControl(SubName=SubName)
        if Name is not None:
            return parent.MenuItemControl(Name=Name)

    @staticmethod
    def clickMenuItemBySubName(
        parent: ui.WindowControl, SubName, waitTime=1, useLoc=False
    ):
        """
        根据控件名称部分内容点击菜单选项。

        参数:
            parent: 菜单所在窗口
            SubName: 菜单控件名称部分内容
            waitTime: 点击后等待时长
            useLoc: 是否转化成坐标进行点击，转成坐标点击是因为有时候点击名称会失败

        返回:
            操作结果
        """
        menuItem = parent.MenuItemControl(SubName=SubName)
        if useLoc:
            x = menuItem.BoundingRectangle.xcenter()
            y = menuItem.BoundingRectangle.ycenter()
            UIA.click(x, y, waitTime)
        else:
            menuItem.Click(waitTime=waitTime)

    @staticmethod
    def clickMenuItemByName(parent: ui.WindowControl, Name, waitTime=1, useLoc=False):
        """
        根据控件名称点击菜单选项。

        参数:
            parent: 菜单所在窗口
            Name: 菜单控件名称
            waitTime: 点击后等待时长
            useLoc: 是否转化成坐标进行点击，转成坐标点击是因为有时候点击名称会失败

        返回:
            操作结果
        """
        menuItem = parent.MenuItemControl(Name=Name)
        if useLoc:
            x = menuItem.BoundingRectangle.xcenter()
            y = menuItem.BoundingRectangle.ycenter()
            UIA.click(x, y, waitTime)
        else:
            menuItem.Click(waitTime=waitTime)

    @staticmethod
    def getControlRectCenter(control: ui.Control):
        """
        获取控件的中心点坐标。

        参数:
            control: 目标控件

        返回:
            控件中心点坐标 (x, y)
        """
        x = control.BoundingRectangle.xcenter()
        y = control.BoundingRectangle.ycenter()
        return x, y

    @staticmethod
    def takeScreenshots(savePath, control=None):
        """
        截图并保存。

        参数:
            savePath: 保存文件路径
            control: 控件对象，默认全屏截图

        返回:
            操作结果
        """
        if control is None:
            control = ui.GetRootControl()
        if not isinstance(control, ui.PaneControl):
            return False, "参数control类型错误"
        control.CaptureToImage(savePath)
        # bitmap = ui.Bitmap.FromControl(control, startX, startY, width, height)
        # bitmap.ToFile(savePath)
        return True, savePath

    @staticmethod
    def showDesktop():
        """
        通过按下 Win + D 显示桌面。
        """
        ui.SendKeys("{Win}d")

    @staticmethod
    def click(x, y, waitTime=0.5):
        """
        鼠标点击指定坐标。

        参数:
            x: 横坐标x值，int类型
            y: 纵坐标y值，int类型
            waitTime: 点击后等待时间

        返回:
            操作结果
        """
        ui.Click(x, y, waitTime)

    @staticmethod
    def rightClick(x, y, waitTime=0.5):
        """
        鼠标右键单击指定坐标。

        参数:
            x: 横坐标x值，int类型
            y: 纵坐标y值，int类型
            waitTime: 点击后等待时间

        返回:
            操作结果
        """
        ui.RightClick(x, y, waitTime)

    @staticmethod
    def dragDrop(startX, startY, endX, endY, moveSpeed=1, waitTime=0.5):
        """
        鼠标拖拽操作（从起始位置拖动到终点位置）。

        参数:
            startX: 起始位置X坐标
            startY: 起始位置Y坐标
            endX: 终点位置X坐标
            endY: 终点位置Y坐标
            moveSpeed: 拖拽速度
            waitTime: 拖拽完成后等待时间

        返回:
            操作结果
        """
        ui.PressMouse(startX, startY, 0.1)
        ui.MoveTo(endX, endY, moveSpeed, 0.1)
        ui.ReleaseMouse(waitTime)

    @staticmethod
    def ComboBoxControl(
        parent: ui.WindowControl, Name=None, AutomationId=None, timeout=3
    ):
        """
        获取下拉列表控件对象。

        参数:
            parent: 所在窗口
            Name: 下拉列表Name属性值
            AutomationId: 下拉列表AutomationId值
            timeout: 超时时间

        返回:
            下拉列表控件对象
        """
        searchProperties = {}
        if Name is not None:
            searchProperties.update({"Name": Name})
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        combox = parent.ComboBoxControl(**searchProperties)
        # 可以判断combox是否存在
        if combox.Exists(timeout):
            return combox
        return None

    @staticmethod
    def selectComboBoxItem(combo: ui.ComboBoxControl, itemName, waitTime=0.5):
        """
        选择下拉框选项。

        参数:
            combo: 待操作的下拉框控件对象
            itemName: 待选择的选项名称
            waitTime: 选择后的等待时长

        返回:
            操作结果

        注意:
            此方法在有些下拉框列表可能不生效，比如用老的QT版本开发的应用
        """
        return combo.Select(itemName=itemName, waitTime=waitTime)

    @staticmethod
    def ListControl(parent: ui.WindowControl, Name=None, AutomationId=None, timeout=3):
        """
        获取列表控件对象。

        参数:
            parent: 所在窗口
            Name: 列表Name属性值
            AutomationId: 列表AutomationId值
            timeout: 超时时间

        返回:
            列表控件对象
        """
        searchProperties = {}
        if Name is not None:
            searchProperties.update({"Name": Name})
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        listControl = parent.ListControl(**searchProperties)
        # 可以判断列表是否存在
        if listControl.Exists(timeout):
            return listControl
        return None

    @staticmethod
    def ListItemControl(parent: ui.ListControl, Name=None, SubName=None, timeout=3):
        """
        获取列表项控件对象。

        参数:
            parent: 父列表控件对象
            Name: Name属性值
            SubName: SubName值
            timeout: 超时时间

        返回:
            列表项控件对象
        """
        if Name is not None:
            itemControl = parent.ListItemControl(Name=Name)
            if itemControl.Exists(timeout):
                return itemControl
        itemControl = parent.ListItemControl(SubName=SubName)
        if itemControl.Exists(timeout):
            return itemControl

    @staticmethod
    def selectListItemByName(
        parent: ui.ListControl, Name=None, SubName=None, waitTime=0.5, isScroll=True
    ):
        """
        根据名称选择列表项。

        参数:
            parent: 父列表控件对象
            Name: 列表项名称完整内容
            SubName: SubName值，列表项名称部分内容
            waitTime: 点击完成后等待时长
            isScroll: 是否允许滚动查找到目标项后再点击

        返回:
            操作结果
        """
        itemControl = UIA.ListItemControl(parent, Name, SubName)
        if itemControl is None:
            return False
        if isScroll:
            itemControl.GetScrollItemPattern().ScrollIntoView()
        itemControl.Click(waitTime=waitTime)
        return True

    @staticmethod
    def ButtonControl(
        parent: ui.WindowControl, Name=None, AutomationId=None, foundIndex=1, timeout=3
    ):
        """
        获取按钮控件对象。

        参数:
            parent: 按钮所在窗口对象
            Name: 按钮名称
            AutomationId: 按钮id
            foundIndex: 寻找按钮所在下标
            timeout: 超时时间

        返回:
            按钮控件对象
        """
        searchProperties = {}
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        button = parent.ButtonControl(
            Name=Name, foundIndex=foundIndex, **searchProperties
        )
        if button.Exists(timeout):
            return button
        return None

    @staticmethod
    def clickButton(button: ui.ButtonControl, waitTime=0.5):
        """
        点击按钮。

        参数:
            button: 待点击的按钮对象
            waitTime: 点击后等待时长

        返回:
            操作结果
        """
        if button is None:
            return False
        button.Click(waitTime=waitTime)
        return True

    @staticmethod
    def RadioButtonControl(
        parent: ui.WindowControl, Name=None, AutomationId=None, foundIndex=1, timeout=3
    ):
        """
        获取单选按钮控件对象。

        参数:
            parent: 单选按钮所在窗口对象
            Name: 单选按钮名称
            AutomationId: 单选按钮id
            foundIndex: 寻找单选按钮所在下标
            timeout: 超时时间

        返回:
            单选按钮控件对象
        """
        searchProperties = {}
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        button = parent.RadioButtonControl(
            Name=Name, foundIndex=foundIndex, **searchProperties
        )
        if button.Exists(timeout):
            return button
        return None

    @staticmethod
    def CheckBoxControl(parent: ui.Control, Name=None, AutomationId=None, timeout=3):
        """
        获取复选框控件对象。

        参数:
            parent: 复选框所在窗口对象
            Name: 复选框名称
            AutomationId: 复选框id
            timeout: 超时时间

        返回:
            复选框控件对象
        """
        searchProperties = {}
        if Name is not None:
            searchProperties.update({"Name": Name})
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        checkBox = parent.CheckBoxControl(**searchProperties)
        # 可以判断复选框是否存在
        if checkBox.Exists(timeout):
            return checkBox
        return None

    @staticmethod
    def selectCheckBox(checkBox: ui.CheckBoxControl, waitTime=0.5):
        """
        选中复选框。

        参数:
            checkBox: 复选框对象
            waitTime: 等待时间

        返回:
            操作结果
        """
        if checkBox.GetTogglePattern().ToggleState == 0:
            checkBox.Click(waitTime=waitTime)
        if checkBox.GetTogglePattern().ToggleState == 1:  # 1是选中状态，0是未选中状态
            return True
        return False

    @staticmethod
    def unSelectCheckBox(checkBox: ui.CheckBoxControl):
        """
        取消勾选复选框。

        参数:
            checkBox: 复选框对象

        返回:
            操作结果
        """
        if checkBox.GetTogglePattern().ToggleState == 1:
            checkBox.Click()
        if checkBox.GetTogglePattern().ToggleState == 0:
            return True
        return False

    @staticmethod
    def ProgressBarControl(
        parent: ui.WindowControl,
        ClassName=None,
        Name=None,
        AutomationId=None,
        timeout=3,
    ):
        """
        获取进度条控件对象。

        参数:
            parent: 进度条所在窗口对象
            ClassName: 进度条类名
            Name: 进度条名称
            AutomationId: 进度条自动化ID
            timeout: 超时时间

        返回:
            进度条控件对象
        """
        searchProperties = {}
        if ClassName is not None:
            searchProperties.update({"ClassName": ClassName})
        if Name is not None:
            searchProperties.update({"Name": Name})
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        progressBar = parent.ProgressBarControl(**searchProperties)
        # 可以判断复选框是否存在
        if progressBar.Exists(timeout):
            return progressBar
        return None

    @staticmethod
    def getProgressBarValue(progressBar: ui.ProgressBarControl):
        """
        获取进度条的当前值。

        参数:
            progressBar: 进度条控件对象

        返回:
            进度条的当前值（整数）
        """
        return int(progressBar.GetLegacyIAccessiblePattern().Value)

    @staticmethod
    def EditControl(
        parent: ui.WindowControl,
        ClassName=None,
        Name=None,
        AutomationId=None,
        timeout=3,
    ):
        """
        获取编辑框控件对象。

        参数:
            parent: 编辑框所在窗口对象
            ClassName: 编辑框类名
            Name: 编辑框名称
            AutomationId: 编辑框自动化ID
            timeout: 超时时间

        返回:
            编辑框控件对象
        """
        searchProperties = {}
        if ClassName is not None:
            searchProperties.update({"ClassName": ClassName})
        if Name is not None:
            searchProperties.update({"Name": Name})
        if AutomationId is not None:
            searchProperties.update({"AutomationId": AutomationId})
        edit = parent.EditControl(**searchProperties)
        # 可以判断编辑框是否存在
        if edit.Exists(timeout):
            return edit
        return None

    @staticmethod
    def inputEditControlText(editControl: ui.EditControl, text):
        """
        向编辑框控件输入文本。

        参数:
            editControl: 编辑框控件对象
            text: 要输入的文本内容

        返回:
            操作结果
        """
        return editControl.GetValuePattern().SetValue(text)
