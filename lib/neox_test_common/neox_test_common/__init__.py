#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NeoX Test Common Library

This package provides common utilities and components for NeoX testing framework,
including configuration file handling, logging setup, and UI automation utilities.

@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import specific functions and classes to avoid F403 warnings
from .common import (
    UIA,
    ProjectConfig,
    dump_json_file,
    dump_toml,
    dump_yml,
    load_json_file,
    load_toml,
    load_yml,
)
from .log import logger

# Define public API
__all__ = [
    # Configuration file utilities
    "load_toml",
    "load_yml",
    "load_json_file",
    "dump_toml",
    "dump_yml",
    "dump_json_file",
    "ProjectConfig",
    # Logging utilities
    "logger",
    # UI Automation utilities
    "UIA",
]

# Package metadata
__version__ = "1.0.0"
__author__ = "KunoLu"
__email__ = "<EMAIL>"
