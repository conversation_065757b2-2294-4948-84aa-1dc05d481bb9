#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   loguru.py
@Software   :   PyCharm
"""

import logging
from typing import Any

from loguru import logger

# Constants
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {thread} | {message}"


class PropagateHandler(logging.Handler):
    """
    Custom logging handler that propagates Loguru messages to the standard logging system.

    This handler bridges Loguru's logging system with Python's standard logging
    module, allowing Loguru log messages to be processed by standard logging
    handlers and formatters. This is useful for integration with existing
    logging infrastructure or third-party libraries that expect standard logging.

    The handler receives log records from Loguru and forwards them to the
    appropriate standard logger based on the record's name attribute.

    Example:
        >>> import logging
        >>> from loguru import logger
        >>>
        >>> # Configure standard logging
        >>> logging.basicConfig(level=logging.INFO)
        >>>
        >>> # Loguru messages will now appear in standard logging output
        >>> logger.info("This message will be propagated")
    """

    def emit(self, record: Any) -> None:
        """
        Process a log record by forwarding it to the standard logging system.

        This method is called by Loguru when a log message needs to be processed.
        It retrieves the appropriate standard logger based on the record's name
        and forwards the record for processing.

        Args:
            record: The log record object containing message data, level,
                   timestamp, and other logging information

        Note:
            This method is called automatically by the logging system and
            should not be called directly by user code.
        """
        # 获取对应的标准日志记录器并处理记录
        logging.getLogger(record.name).handle(record)


# 移除 Loguru 的默认处理器以避免重复输出
logger.remove(handler_id=None)

# 添加自定义的传播处理器，将 Loguru 日志传播到标准日志系统
logger.add(
    sink=PropagateHandler(),
    format=LOG_FORMAT,
    backtrace=True,
    diagnose=True,
)
