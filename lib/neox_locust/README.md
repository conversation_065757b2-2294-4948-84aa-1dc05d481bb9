# NeoX Locust

[![Python Version](https://img.shields.io/badge/python-3.10%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

A specialized Locust performance testing library for NeoX applications, providing task modules for prescription processing, survey handling, MongoDB operations, and recognition engine testing.

## Features

### 🧪 Core Task Modules
- **Enquete Tasks**: Survey/questionnaire processing with MongoDB integration
- **Kenta Backend Tasks**: Backend prescription processing with database operations
- **Kenta Frontend Tasks**: Frontend prescription processing with complete API workflows
- **Recognition Engine Tasks**: GPU-based image recognition testing (OCR and QR codes)

### 📊 MongoDB Integration
- **Document Construction**: Automated MongoDB document generation for prescriptions and surveys
- **Timestamp Management**: Japanese timezone timestamp handling with random increments
- **Batch Operations**: Support for single and bulk database operations

### 🛠️ Utility Functions
- **Device Management**: Device ID retrieval and API authentication
- **File Processing**: Multi-format file handling (JPEG, PDF) with MIME type detection
- **Multipart Uploads**: File upload testing with custom boundaries and form data

## Installation

```bash
pip install neox-locust
```

Or install from source:

```bash
<NAME_EMAIL>:neoxinc/testing.git
cd lib/neox_locust
pip install -e .
```

## Quick Start

### Survey Processing Example

```python
from locust import HttpUser
from neox_locust.enquete.enquete import EnqueteTask
from neox_locust.mongo.mongo import construct_document
from locust_common.frame.MongoDB import MongoDBUser

class SurveyUser(MongoDBUser):
    tasks = [EnqueteTask]
    conn_string = "mongodb://localhost:27017"
    db_name = "survey_db"
    collection = "enquete_collection"
    
    def on_start(self):
        super().on_start()
        # Initialize parameter queue for survey data
        from locust_common.param.DQueue import data_queue
        from pathlib import Path
        self.param_data_enquete_queue = data_queue(Path("survey_params.csv"))
```

### Backend Prescription Processing Example

```python
from locust import HttpUser
from neox_locust.kenta.be_prescriptions import BEPresTask
from locust_common.frame.MongoDB import MongoDBUser

class BackendPresUser(MongoDBUser):
    tasks = [BEPresTask]
    conn_string = "mongodb://localhost:27017"
    db_name = "prescription_db"
    collection = "prescriptions"
    
    def on_start(self):
        super().on_start()
        # Initialize parameter queue for prescription data
        from locust_common.param.DQueue import data_queue
        from pathlib import Path
        self.param_data_pres_queue = data_queue(Path("prescription_params.csv"))
```

### Frontend Prescription Processing Example

```python
from locust import HttpUser
from neox_locust.kenta.fe_prescriptions import FEPresTask

class FrontendPresUser(HttpUser):
    tasks = [FEPresTask]
    host = "https://api.example.com"
    
    def on_start(self):
        # Initialize parameter queue for prescription data
        from locust_common.param.DQueue import data_queue
        from pathlib import Path
        self.param_data_pres_queue = data_queue(Path("prescription_params.csv"))
```

### Recognition Engine Testing Example

```python
from locust import HttpUser
from neox_locust.recognition.engine import EngineTask

class RecognitionUser(HttpUser):
    tasks = [EngineTask]
    host = "https://recognition-api.example.com"
    
    def on_start(self):
        # Initialize parameter queue for image data
        from locust_common.param.DQueue import data_queue
        from pathlib import Path
        self.param_data_images_queue = data_queue(Path("image_params.csv"))
```

### MongoDB Document Construction

```python
from neox_locust.mongo.mongo import construct_document
from loguru import logger

# Create single document for prescription
document = construct_document(
    operation="insert_one",
    log_flag=True,
    clog=logger,
    merchant_id="12345",
    device_id="device001",
    s3_path="/images/prescriptions",
    image_name="prescription_1_1.jpg",
    image_category="r"  # 'r' for prescription, 'e' for enquete
)

# Create multiple documents for batch insertion
documents = construct_document(
    operation="insert_many",
    log_flag=True,
    clog=logger,
    merchant_id="12345",
    device_id="device001",
    s3_path="/images/surveys",
    image_name="survey1.jpg|survey2.jpg",
    image_category="e"
)
```

### Device Management

```python
from neox_locust.kenta.general import get_device_id
from loguru import logger

headers = {
    "Authorization": "Bearer your-token",
    "Content-Type": "application/json"
}

device_response = get_device_id(
    env="api.example.com",
    api_version="v1",
    headers=headers,
    log_flag=True,
    clog=logger
)

if device_response.get("status_code") == 200:
    device_id = device_response.get("deviceId")
    print(f"Device ID: {device_id}")
```

## Module Documentation

### Enquete Module (`neox_locust.enquete`)

#### EnqueteTask
- **Purpose**: Survey/questionnaire processing task set
- **Key Features**: MongoDB integration, parameter queue management, configurable timing
- **Use Case**: Load testing survey submission and processing systems

### Kenta Module (`neox_locust.kenta`)

#### BEPresTask (Backend Prescriptions)
- **Purpose**: Backend prescription processing simulation
- **Key Features**: MongoDB document creation, prescription metadata handling
- **Use Case**: Database performance testing for prescription systems

#### FEPresTask (Frontend Prescriptions)
- **Purpose**: Frontend prescription processing with complete API workflow
- **Key Features**: File uploads, QR code processing, asynchronous polling
- **Use Case**: End-to-end API testing for prescription processing systems

#### General Utilities
- **Purpose**: Common utilities for device management and API interactions
- **Key Features**: Device ID retrieval, error handling, timeout management
- **Use Case**: Setup and authentication for prescription processing tests

### MongoDB Module (`neox_locust.mongo`)

#### Document Construction
- **Purpose**: MongoDB document generation for testing data
- **Key Features**: Automatic timestamp generation, multi-image support, category handling
- **Supported Operations**: `find`, `insert_one`, `insert_many`
- **Use Case**: Creating realistic test data for database performance testing

### Recognition Module (`neox_locust.recognition`)

#### EngineTask
- **Purpose**: GPU-based recognition engine testing
- **Key Features**: Image file uploads, OCR/QR recognition validation, custom assertions
- **Use Case**: Performance testing of image recognition APIs

## Advanced Usage

### Custom Parameter Files

Create CSV files with appropriate columns for different modules:

#### Survey Parameters (survey_params.csv)
```csv
merchant_id,device_id,s3_path,image_name,image_category
12345,device001,/surveys,survey1.jpg,e
12345,device002,/surveys,survey2.jpg,e
```

#### Prescription Parameters (prescription_params.csv)
```csv
merchant_id,device_id,s3_path,image_name,image_category,study_id,usr_cookie
67890,device003,/prescriptions,prescription_1_1.jpg,r,study123,cookie123
67890,device004,/prescriptions,prescription_2_1.jpg,r,study124,cookie124
```

#### Image Recognition Parameters (image_params.csv)
```csv
image_file_name
test_image1.jpg
test_image2.pdf
qr_code_sample.jpg
```

### Configuration Management

Configure test scenarios using TOML files:

```toml
[mongodb]
operation_type = "insert_one"
connection_string = "mongodb://localhost:27017"

[timing]
enquete_iter_time = { iter_time_fmt = "between", between_iter_time = [1.0, 3.0], digits = 2 }
pres_iter_time = { iter_time_fmt = "constant", const_iter_time = 2 }

[parameters]
strategy_enquete = true
strategy_pres = true

[logging]
locust_log_flag = true
log_level = "DEBUG"
```

### File Upload Testing

Set up file directories for upload testing:

```
payload/
└── pres_task/
    ├── v1/
    │   ├── apply.json
    │   ├── fragment.json
    │   └── uploadQuery.json
    └── images/
        ├── prescription1.pdf
        ├── prescription2.jpg
        └── survey1.jpeg
```

### Custom Assertion Functions

```python
def custom_recognition_assert(response):
    """Custom assertion for recognition engine responses"""
    if response.status_code != 200:
        return False
    
    try:
        data = response.json()
        result = data.get("result")
        
        # Check for valid QR content or OCR content
        if result is None:
            return True
        
        qr_content = result.get("qrContent")
        ocr_content = result.get("ocrContent")
        
        return (isinstance(qr_content, (int, str)) or
                (isinstance(ocr_content, dict) and len(ocr_content) > 0))
    except Exception:
        return False

# Use in recognition tasks
func_frame.task_assert_resp_with_file_upload(
    obj=self,
    file_path=image_path,
    file_field_name="image_file",
    content_type="image/jpeg",
    form_data={"mode": "ocr"},
    custom_assert_func=custom_recognition_assert
)
```

## Requirements

- Python 3.10+
- Locust 2.0+
- locust-common >= 0.1.0
- Dependencies: `pendulum`, `requests`, `requests-toolbelt`, `demjson3`

## Configuration

The library uses configuration files and environment variables through the `locust_common.param.ExprJmesMap` system. Key configuration items include:

- **API_VERSION**: API version for endpoint construction
- **ENV_HOST**: Environment hostname
- **MONGO_OPER_TYPE**: MongoDB operation type
- **PARAM_STRATEGY_***: Parameter recycling strategies
- **ITER_TIME_CONF**: Iteration timing configurations
- **LOCUST_LOG_FLAG**: Logging enable flag

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
<NAME_EMAIL>:neoxinc/testing.git
cd lib/neox_locust
pip install -e ".[dev]"
```

### Running Tests

```bash
# Test individual modules
locust -f test_enquete.py
locust -f test_backend_prescriptions.py
locust -f test_frontend_prescriptions.py
locust -f test_recognition_engine.py
```

## License

This project is licensed under the MIT License.

## Support

- **Address**: [Bitbucket](https://bitbucket.org/neoxinc/testing/src/main/)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed history of changes.