#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-20
<AUTHOR>   <PERSON>noLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

"""
NeoX Locust Package

This package provides specialized Locust performance testing utilities for NeoX applications,
including modules for survey processing, prescription handling, MongoDB operations, and recognition engine testing.

Modules:
    enquete: Survey/questionnaire processing task modules
    kenta: Prescription recognition and processing workflows (backend and frontend)
    mongo: MongoDB document construction and database testing utilities
    recognition: Recognition engine testing framework for image processing
"""
