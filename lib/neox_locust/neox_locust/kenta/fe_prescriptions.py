#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-10-12
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   fe_prescriptions.py
@Software   :   Cursor
"""


# import os
# import time
# import orjson as json
# from copy import deepcopy
# from jmespath import search
# from itertools import repeat

import random
import uuid
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Type

from locust import SequentialTaskSet, task
from locust_common.frame.TaskFrame import FuncFrame
from locust_common.function.func_conf import load_json_file
from locust_common.function.func_param import custom_iter_time
from locust_common.param.ExprJmesMap import ExprJmespathMapping as ejm

# from locust_common.function.func_log import set_log
from locust_common.param.ExprJmesMap import get_conf
from loguru import logger
from loguru._logger import Logger
from requests_toolbelt import MultipartEncoder

# Constants
PAYLOAD_DIR = Path.cwd().joinpath("payload")
PAYLOAD_TASK_DIR = PAYLOAD_DIR.joinpath("pres_task")
HEADERS_PATH = ["headers", "base_headers.json"]
SUPPORTED_EXTENSIONS = {".jpg", ".jpeg", ".pdf"}
MIME_TYPE_MAPPING = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".pdf": "application/pdf",
}

# Load base headers
HEADERS = load_json_file(PAYLOAD_DIR.joinpath(*HEADERS_PATH))


class FEPresTask(SequentialTaskSet):
    """
    Frontend prescription processing task set for Locust performance testing.

    This class simulates prescription recognition processing from the frontend,
    implementing the complete API workflow including file uploads, QR code processing,
    and asynchronous result polling.

    API Call Flow:
        apply ->
            if QR:
                qr -> fragment ->
            uploadFile -> uploadQuery -> wait 9 seconds -> ids

    Timer (ids) Processing Logic:
        for qrList in ids response:
            if qrList is not None:
                statistics (Deprecated) -> qrDownload
            else:
                wait 2 seconds
    """

    def on_start(self) -> None:
        """
        Initialize task configuration when the user starts.

        Sets up API endpoints, file paths, HTTP configurations, and logging
        for the frontend prescription processing testing session.
        """
        # 获取 API 版本配置
        api_version: str = get_conf(ejm.API_VERSION)  # type: ignore

        # 配置环境主机和断言列表
        self.env_host: str = get_conf(ejm.ENV_HOST)  # type: ignore
        self.assert_keys: List[str] = []

        # 配置 HTTP 请求方法
        self.method = "post"

        # 配置文件上传目录
        self.upload_file_dir: str = get_conf(ejm.PARAM_PRES_UPLOAD_DIRNAME)  # type: ignore

        # 处理上传文件类型和路径
        self.upload_file_mime_type, self.upload_files_path = self._dealing_pres_files()

        # 配置 API 端点 URL
        self._setup_api_endpoints(api_version)

        # 加载请求体配置
        self._load_payload_configurations(api_version)

        # 配置日志处理器
        self.locust_log_flag: bool = get_conf(ejm.LOCUST_LOG_FLAG)  # type: ignore
        self.internal_log_flag: bool = get_conf(ejm.LOCUST_LOG_FLAG)  # type: ignore
        self.internal_log: Optional[Type[Logger]] = (
            ejm.LOCUST_LOG_FLAG.set_internal_log()
        )  # type: ignore

    def _setup_api_endpoints(self, api_version: str) -> None:
        """
        Set up API endpoint URLs for different operations.

        Args:
            api_version: API version string for URL construction
        """
        # 设备管理相关端点
        self.url_device = f"/api/w/{api_version}/neox/vision/merchant/device"

        # 主要处理流程端点
        self.url_apply = f"/api/w/{api_version}/prescription/async/apply"
        self.url_fragment = f"/api/w/{api_version}/prescription/async/fragment"
        self.url_uploadFile = f"/api/w/{api_version}/prescription/async/uploadFile"
        self.url_uploadQuery = f"/api/w/{api_version}/prescription/async/uploadQuery"
        self.url_qrDownload = f"/api/w/{api_version}/prescription/async/qrDownload"

        # 快速轮询端点
        self.url_qrList_ids = f"/api/w/{api_version}/prescription/async/qrList/ids"

    def _load_payload_configurations(self, api_version: str) -> None:
        """
        Load JSON payload configurations for different API operations.

        Args:
            api_version: API version for loading version-specific payloads
        """
        # 配置请求体文件路径
        payload_path_api_version = PAYLOAD_TASK_DIR.joinpath(api_version)

        # 加载各种操作的请求体配置
        self.payload_apply = load_json_file(
            payload_path_api_version.joinpath("apply.json")
        )
        self.payload_fragment = load_json_file(
            payload_path_api_version.joinpath("fragment.json")
        )
        self.payload_uploadQuery = load_json_file(
            payload_path_api_version.joinpath("uploadQuery.json")
        )
        self.payload_qrDownload = load_json_file(
            payload_path_api_version.joinpath("qrDownload.json")
        )
        self.payload_qrList_ids = load_json_file(
            payload_path_api_version.joinpath("qrList_ids.json")
        )

    def _dealing_pres_files(self) -> Tuple[str, List[Path]]:
        """
        Filter and categorize prescription files by format.

        Scans the upload directory for supported file formats and returns
        MIME type information and file path lists for testing.

        Returns:
            Tuple containing:
                - MIME type string for the first supported file type found
                - List of file paths for supported formats

        Example:
            >>> mime_type, file_paths = self._dealing_pres_files()
            >>> print(mime_type)  # "image/jpeg"
            >>> print(len(file_paths))  # 10
        """
        # 构建处方文件目录路径
        pres_dir_path = PAYLOAD_TASK_DIR.joinpath(self.upload_file_dir)

        # 收集符合条件的文件
        valid_files: List[Path] = []
        first_mime_type = "application/octet-stream"  # 默认 MIME 类型

        # 遍历目录中的所有文件
        for file_path in pres_dir_path.glob("**/*"):
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in SUPPORTED_EXTENSIONS:
                    valid_files.append(file_path)
                    # 记录第一个找到的 MIME 类型
                    if len(valid_files) == 1:
                        first_mime_type = MIME_TYPE_MAPPING.get(
                            ext, "application/octet-stream"
                        )

        return first_mime_type, valid_files

    def _get_headers(
        self, param_data: Dict[str, Any], project_id: str, cookie: str
    ) -> Dict[str, str]:
        """
        Generate HTTP headers for file management operations.

        Args:
            param_data: Parameter data from the test queue
            project_id: Project identifier
            cookie: Session cookie string

        Returns:
            Dictionary containing HTTP headers for the request
        """
        # 基于基础头部创建文件管理头部
        file_headers = HEADERS.copy()  # type: ignore
        file_headers.update(  # type: ignore
            {
                "Cookie": cookie,
                "X-Project-ID": project_id,
                "Content-Type": "application/json",
            }
        )
        return file_headers  # type: ignore

    @task
    def apply(self) -> None:
        """
        Execute the application placeholder request.

        This task represents the first step in the prescription processing
        workflow, where the system applies for a processing slot.
        """
        display_name = "[ 处方笺处理 ]: 01_申请占位"

        # 从队列中获取处方参数数据
        param_data_pres_task: Dict[str, Any] = self.user.param_data_pres_queue.get()  # type: ignore
        project_id = param_data_pres_task["study_id"]
        cookie = f"usr={param_data_pres_task['usr_cookie']}"

        # 根据参数化策略决定是否将数据放回队列
        if get_conf(ejm.PARAM_STRATEGY_PRES):  # type: ignore
            self.user.param_data_pres_queue.put_nowait(param_data_pres_task)  # type: ignore

        # 生成请求头部信息
        self.file_management_headers = self._get_headers(
            param_data_pres_task, project_id, cookie
        )

        # 执行申请占位请求
        _ = FuncFrame(
            url=self.url_apply,
            method=self.method,
            name=display_name,
            headers=self.file_management_headers,
            body=self.payload_apply,
            assert_keys=self.assert_keys,
            locust_logger=logger,
            log_flag=self.locust_log_flag,
        ).task_assert_resp_by_json(self)

    @task
    def fragment(self) -> None:
        """
        Execute QR code fragment processing.

        This task handles QR code fragment operations when QR codes
        are detected in the uploaded prescription images.
        """
        display_name = "[ 处方笺处理 ]: 02_获取QR码片段"

        # 执行 QR 码片段处理请求
        _ = FuncFrame(
            url=self.url_fragment,
            method=self.method,
            name=display_name,
            headers=self.file_management_headers,
            body=self.payload_fragment,
            assert_keys=self.assert_keys,
            locust_logger=logger,
            log_flag=self.locust_log_flag,
        ).task_assert_resp_by_json(self)

    @task
    def upload_file(self) -> None:
        """
        Execute file upload operation.

        This task uploads prescription image files using multipart/form-data
        encoding with randomly selected files from the configured directory.
        """
        display_name = "[ 处方笺处理 ]: 03_上传文件"

        # 随机选择一个文件进行上传
        file_path = random.choice(self.upload_files_path)
        file_id = "670de635173bcb4f3278c076"  # 固定的文件 ID（测试用）
        file_name = file_path.stem
        custom_boundary = str(uuid.uuid4())

        # 构建多部分表单数据
        multipart_data = MultipartEncoder(
            fields={
                "id": file_id,
                "originFileName": file_name,
                "retryTimes": "0",
                "fileStatus": "1",
                "multiPages": "System.Collections.Generic.Dictionary`2[System.String,System.Object]",
                "file": (
                    "tmp.jpeg",
                    open(file_path.as_posix(), "rb"),
                    self.upload_file_mime_type,
                ),
            },
            boundary=custom_boundary,
        )

        # 更新请求头部以支持文件上传
        upload_headers = self.file_management_headers.copy()
        upload_headers["Content-Type"] = (
            f"multipart/form-data; boundary={multipart_data.boundary}"
        )

        try:
            # 执行文件上传请求
            _ = FuncFrame(
                url=self.url_uploadFile,
                method=self.method,
                name=display_name,
                headers=upload_headers,
                body=multipart_data,
                assert_keys=self.assert_keys,
                locust_logger=logger,
                log_flag=self.locust_log_flag,
            ).task_assert_resp_by_str(self)

        except Exception as e:
            # 处理文件上传异常
            if self.internal_log_flag and self.internal_log:
                self.internal_log.error(f"文件上传失败: {str(e)}")  # type: ignore

    @task
    def upload_query(self) -> None:
        """
        Execute upload result query operation.

        This task queries the upload processing status after file upload
        to verify that the file has been successfully processed.
        """
        display_name = "[ 处方笺处理 ]: 04_查询上传结果"

        # 执行上传结果查询请求
        _ = FuncFrame(
            url=self.url_uploadQuery,
            method=self.method,
            name=display_name,
            headers=self.file_management_headers,
            body=self.payload_uploadQuery,
            assert_keys=self.assert_keys,
            locust_logger=logger,
            log_flag=self.locust_log_flag,
        ).task_assert_resp_by_json(self)

    @task
    def qr_list_ids(self) -> None:
        """
        Execute rapid polling for QR code processing results.

        This task implements the rapid polling mechanism to check
        for QR code processing completion and retrieve result IDs.
        """
        display_name = "[ 处方笺处理 ]: 05_快轮询"

        # 执行快速轮询请求
        _ = FuncFrame(
            url=self.url_qrList_ids,
            method=self.method,
            name=display_name,
            headers=self.file_management_headers,
            body=self.payload_qrList_ids,
            assert_keys=self.assert_keys,
            locust_logger=logger,
            log_flag=self.locust_log_flag,
        ).task_assert_resp_by_json(self)

    @task
    def statistics(self) -> None:
        """
        Execute statistics query operation (Deprecated).

        This task queries processing statistics information.
        Note: This endpoint is deprecated in newer versions.
        """
        display_name = "[ 处方笺处理 ]: 06_查询统计信息（已弃用）"

        # 记录弃用警告
        if self.internal_log_flag and self.internal_log:
            self.internal_log.warning("statistics 接口已弃用，建议使用其他接口")  # type: ignore

    @task
    def qr_download(self) -> None:
        """
        Execute QR code download operation.

        This task downloads the generated QR code files after
        prescription processing has been completed.
        """
        display_name = "[ 处方笺处理 ]: 07_下载二维码"

        # 执行二维码下载请求
        _ = FuncFrame(
            url=self.url_qrDownload,
            method=self.method,
            name=display_name,
            headers=self.file_management_headers,
            body=self.payload_qrDownload,
            assert_keys=self.assert_keys,
            locust_logger=logger,
            log_flag=self.locust_log_flag,
        ).task_assert_resp_by_json(self)

    @task
    def poll(self) -> None:
        """
        Execute slow polling operation (Not used in load testing).

        This task represents the slow polling mechanism, but is not
        used in the current load testing scenarios.
        """
        _ = "[ 处方笺处理 ]: 08_慢轮询（测试中不使用）"

        # 记录信息日志
        if self.internal_log_flag and self.internal_log:
            self.internal_log.info("慢轮询接口在压力测试中不使用")  # type: ignore

    @task
    def stop(self) -> None:
        """
        Stop task to control iteration timing and execution flow.

        This task determines when the current iteration should end and
        implements custom timing control for prescription processing workflows.
        """
        # 根据配置执行自定义迭代时间控制
        _ = custom_iter_time(get_conf(ejm.PRES_ITER_TIME_CONF))  # type: ignore

        # 结束当前迭代
        self.interrupt()
