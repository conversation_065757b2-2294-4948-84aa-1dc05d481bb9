#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2025-01-13
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   mongo.py
@Software   :   Cursor
"""

import random
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Type, Union

import pendulum
from bson import Int64, ObjectId
from loguru._logger import Logger
from pendulum import DateTime

# Constants
ENQUETE_CATEGORY = "e"
ROTATION_CATEGORY = "r"
DEFAULT_PAGE_COUNT = 1
DEFAULT_PAGE_NUMBER = 1


def get_object_id() -> ObjectId:
    """
    Generate a new MongoDB ObjectId.

    Returns:
        ObjectId: A new MongoDB ObjectId instance

    Example:
        >>> obj_id = get_object_id()
        >>> print(type(obj_id))
        <class 'bson.objectid.ObjectId'>
    """
    return ObjectId()


def _get_jp_ts() -> <PERSON><PERSON>[int, float, str]:
    """
    Get current timestamp in multiple formats for Japanese timezone.

    Generates timestamps in different formats based on Japan Standard Time (JST),
    including epoch seconds, trimmed float, and ISO 8601 string.

    Returns:
        Tuple containing:
            - int: Timestamp in seconds (epoch time)
            - float: Trimmed timestamp with 3 decimal places
            - str: ISO 8601 formatted datetime string in UTC

    Example:
        >>> seconds, trimmed, iso = _get_jp_ts()
        >>> print(f"Seconds: {seconds}, Trimmed: {trimmed}")
        Seconds: 1674567890, Trimmed: 1674567890.123
    """
    # 获取日本时区的当前时间
    jp_time = pendulum.now("Asia/Tokyo")
    jp_time_ts = jp_time.timestamp()
    jp_time_seconds = int(jp_time_ts)
    jp_time_trimmed = round(jp_time_ts, 3)
    iso_date = jp_time.in_tz("UTC").to_iso8601_string()

    return jp_time_seconds, jp_time_trimmed, iso_date


def _ts_random_increment(time_trimmed: float) -> List[float]:
    """
    Generate randomly incremented timestamps for process timing simulation.

    Creates a list of timestamps with random increments to simulate
    different processing stages (ap, su, up) in the document lifecycle.

    Args:
        time_trimmed: Base timestamp to increment from

    Returns:
        List of 3 randomly incremented timestamps with 3 decimal precision

    Example:
        >>> base_time = 1674567890.123
        >>> process_times = _ts_random_increment(base_time)
        >>> print(len(process_times))  # 3
        >>> print(all(t > base_time for t in process_times))  # True
    """
    random_ts_list = []

    # 生成三个处理阶段的随机时间戳
    for i in range(3):
        random_increment = random.uniform(0, 5)
        time_trimmed += random_increment
        random_ts_list.append(round(time_trimmed, 3))

    return random_ts_list


def _iso_random_increment(iso_date: str) -> List[DateTime]:
    """
    Generate randomly incremented ISO datetime objects.

    Creates datetime objects with random increments for document
    created and updated timestamps.

    Args:
        iso_date: ISO 8601 formatted datetime string

    Returns:
        List of 2 DateTime objects with random increments

    Example:
        >>> iso_str = "2023-01-24T12:34:56+00:00"
        >>> datetimes = _iso_random_increment(iso_str)
        >>> print(len(datetimes))  # 2
    """
    iso_date_list = []
    iso_date_time = pendulum.parse(iso_date)

    # 生成创建时间和更新时间（随机递增）
    for i in range(2):
        random_increment = int(random.uniform(1, 100))
        iso_date_time = iso_date_time.add(seconds=random_increment)  # type: ignore
        iso_date_list.append(iso_date_time)

    return iso_date_list


def construct_document(
    operation: str, log_flag: bool, clog: Optional[Type[Logger]], **kwargs: Any
) -> Union[Dict[str, Any], List[Dict[str, Any]], None]:
    """
    Construct MongoDB documents for prescription or survey data.

    This function creates properly formatted MongoDB documents based on the
    specified operation type. It supports different document structures for
    prescriptions and surveys, with appropriate metadata and timestamps.

    Args:
        operation: MongoDB operation type ("find", "insert_one", "insert_many")
        log_flag: Whether to enable debug logging
        clog: Logger instance for debug output
        **kwargs: Document construction parameters including:
            - merchant_id: Merchant identifier
            - device_id: Device identifier
            - s3_path: S3 storage path for images
            - image_name: Image filename(s), can contain multiple files separated by "|"
            - image_category: Image category ("e" for enquete, "r" for prescription)

    Returns:
        Single document dict for "find"/"insert_one", list of documents for
        "insert_many", or None for unsupported operations

    Example:
        >>> doc = construct_document(
        ...     operation="insert_one",
        ...     log_flag=True,
        ...     clog=logger,
        ...     merchant_id="12345",
        ...     device_id="device001",
        ...     s3_path="/images/path",
        ...     image_name="test_1_1.jpg",
        ...     image_category="r"
        ... )
        >>> print(doc["merchantId"])
        '12345'
    """

    def _iterate_documents() -> List[Dict[str, Any]]:
        """
        Generate document list for batch operations.

        Iterates through image names and creates individual documents
        for each image with proper metadata and timestamps.

        Returns:
            List of constructed document dictionaries
        """
        documents = []

        # 提取基础参数化数据（document 中的必要字段）
        merchant_id = kwargs.get("merchant_id")
        device_id = kwargs.get("device_id")
        s3_path = kwargs.get("s3_path")
        img_names = kwargs.get("image_name", "")
        img_category = kwargs.get("image_category")

        # 生成同一组图片下的公共字段信息（时间戳）
        jp_ts_secs, jp_ts_trimmed, iso_date = _get_jp_ts()

        # 解析图片名称，支持多张图片（用 | 分隔）
        if "|" in img_names and any(ext in img_names for ext in ["jpeg", "jpg"]):
            files_name = [file.strip() for file in img_names.split("|")]
        else:
            files_name = [img_names.strip()]

        # 遍历每张图片，生成对应的 MongoDB 文档
        for img_name in files_name:
            if img_category == ENQUETE_CATEGORY:
                # 调查问卷类型：单张图片，页数和页码均为 1
                file_name, page_count, page = (
                    img_name,
                    DEFAULT_PAGE_COUNT,
                    DEFAULT_PAGE_NUMBER,
                )
            else:
                # 处方类型：从文件名解析页数信息（格式：文件名_页数_页码）
                file_info = Path(img_name).stem.split("_")
                file_name = file_info[0].strip()
                page_count = (
                    int(file_info[1].strip())
                    if len(file_info) > 1
                    else DEFAULT_PAGE_COUNT
                )
                page = (
                    int(file_info[2].strip())
                    if len(file_info) > 2
                    else DEFAULT_PAGE_NUMBER
                )

            # 生成文档的唯一标识和时间信息
            oid = get_object_id()
            process_time_list = _ts_random_increment(jp_ts_trimmed)
            created_time, updated_time = _iso_random_increment(iso_date)
            origin_file_name = (
                file_name if img_category == ENQUETE_CATEGORY else f"{file_name}.pdf"
            )

            # 构建 MongoDB 文档结构
            document: Dict[str, Any] = {
                "_id": oid,
                "clientType": 5,
                "jahisVersion": 9,
                "merchantId": merchant_id,
                "deviceId": device_id,
                "shootType": 5,
                "status": 1,
                "timestamp": Int64(jp_ts_secs),
                "timestampList": [str(jp_ts_secs)],
                "rcModel": 0,
                "originFileName": origin_file_name,
                "pageCount": page_count,
                "imagePath": f"{s3_path}/{img_name}",
                "imageList": [f"{s3_path}/{img_name}"],
                "page": page,
                "createdAt": created_time,
                "updatedAt": updated_time,
                "valid": 1,
                "processTime": {
                    "ap": process_time_list[0],
                    "su": process_time_list[1],
                    "up": process_time_list[2],
                },
            }

            # 添加预处理信息（适用于新版本 Kenta >= v1.9.9）
            if img_category is not None:
                document["pretreatment"] = {
                    "type": ROTATION_CATEGORY,
                    "r": 0,
                    "category": img_category,
                    "conf": 1,
                }

            # 调查问卷类型需要额外的 category 字段
            if img_category == ENQUETE_CATEGORY:
                document["category"] = img_category

            # 添加文档到列表
            documents.append(document)

            # 记录调试日志
            if log_flag and clog:
                clog.debug(f"[ ObjectId ] -> {oid}")  # type: ignore
                clog.debug(f"[ originFileName ] -> {origin_file_name}")  # type: ignore
                clog.debug(f"[ Document ] -> {document}")  # type: ignore

        # 记录总体调试日志
        if log_flag and clog:
            clog.debug(f"[ Documents ] -> {documents}")  # type: ignore

        return documents

    # 根据操作类型返回相应的文档格式
    match operation:
        case "find" | "insert_one":
            # 单文档操作：返回第一个文档
            return _iterate_documents()[0]
        case "insert_many":
            # 批量插入操作：返回文档列表
            return _iterate_documents()
        case _:
            # 不支持的操作类型：记录错误并返回 None
            if log_flag and clog:
                clog.error(f"[ Unsupported Operation ] -> {operation}")  # type: ignore
            return None
