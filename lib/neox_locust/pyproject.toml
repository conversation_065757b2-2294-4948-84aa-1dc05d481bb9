[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "neox-locust"
version = "0.1.0"
description = "A basic locust function for NeoX performance testing"
requires-python = ">=3.10"
dependencies = [
    "demjson3>=3.0.6",
    "locust-common>=0.1.0",
    "pendulum>=3.0.0",
    "requests>=2.32.3",
    "requests_toolbelt>=1.0.0",
]
authors = [{ name = "Kuno Lu", email = "<EMAIL>" }]
readme = { file = "README.md", content-type = "text/markdown" }
license = { text = "MIT License" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/src/main/"
