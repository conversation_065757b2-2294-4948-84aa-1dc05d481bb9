# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Comprehensive English docstrings for all classes and methods
- Type hints throughout the codebase for better IDE support
- Standardized file headers following project conventions
- Enhanced error handling with proper exception management

### Changed
- Code formatting standardized across all modules
- Import organization improved for better readability
- Function signatures updated with proper type annotations
- Logging implementation unified with locust_common standards

### Fixed
- Type compatibility issues in MongoDB document construction
- File path handling for cross-platform compatibility
- Exception handling in HTTP request operations

## [0.1.0] - 2025-01-25

### Added
- **Core Task Modules**
  - `EnqueteTask`: Survey/questionnaire processing task set with MongoDB integration
  - `BEPresTask`: Backend prescription processing with database operations
  - `FEPresTask`: Frontend prescription processing with complete API workflows
  - `EngineTask`: GPU-based recognition engine testing framework

- **MongoDB Integration Module**
  - `construct_document`: Automated MongoDB document generation for prescriptions and surveys
  - ObjectId generation utilities with proper BSON formatting
  - Japanese timezone timestamp handling with random increments for realistic testing
  - Support for single document (`insert_one`, `find`) and batch operations (`insert_many`)

- **Utility Functions**
  - `get_device_id`: Device management and API authentication utilities
  - File processing with multi-format support (JPEG, PDF)
  - MIME type detection and validation
  - Multipart form data encoding for file uploads

### Features

#### Enquete Module (Survey Processing)
- MongoDB-based survey data handling and storage
- Parameterized testing with CSV file support
- Configurable iteration timing and execution flow
- Integration with locust_common parameter management
- Support for survey image processing and metadata

#### Kenta Module (Prescription Processing)

**Backend Processing (BEPresTask)**
- MongoDB document construction for prescription data
- Automatic timestamp generation with Japanese timezone support
- Parameter queue management with recycling strategies
- Logging integration with debug and error tracking
- Support for prescription image metadata and categorization

**Frontend Processing (FEPresTask)**
- Complete API workflow simulation for prescription processing
- Multi-step process: apply → fragment → uploadFile → uploadQuery → polling → download
- File upload testing with multipart/form-data encoding
- Asynchronous result polling (rapid and slow polling mechanisms)
- QR code processing and download functionality
- Custom boundary generation for file uploads
- Error handling and retry mechanisms

**General Utilities**
- Device ID retrieval from NeoX vision merchant endpoints
- HTTP request timeout and error handling
- JSON response parsing and validation
- Authentication header management

#### MongoDB Module (Database Operations)
- **Document Construction**: Automated generation of MongoDB documents with proper structure
- **Multi-image Support**: Handle single and multiple image processing scenarios
- **Timestamp Management**: Japanese timezone timestamps with realistic random increments
- **Category Support**: Distinction between prescription ('r') and survey ('e') categories
- **File Name Parsing**: Automatic extraction of page count and page numbers from filenames
- **Process Time Simulation**: Three-stage processing time tracking (ap, su, up)

#### Recognition Module (Image Recognition Testing)
- GPU-based recognition engine testing framework
- Image file upload with automatic MIME type detection
- Custom assertion logic for OCR and QR code recognition results
- Support for both OCR text extraction and QR code content validation
- Configurable recognition modes (OCR, QR, mixed)
- Performance metrics collection for recognition operations
- Error handling for invalid images and processing failures

### Technical Improvements
- **Type Safety**: Comprehensive type hints for all functions and methods
- **Error Handling**: Robust exception handling with specific error types
- **Logging Integration**: Unified logging with locust_common standards
- **Configuration Management**: Integration with ExprJmesMap configuration system
- **Performance**: Optimized file processing and MongoDB operations

### Dependencies
- `locust-common`: Core testing framework integration and utilities
- `pendulum`: Advanced datetime handling with timezone support
- `requests`: HTTP client for API interactions
- `requests-toolbelt`: Multipart form data encoding for file uploads
- `demjson3`: Enhanced JSON processing capabilities

### Configuration Support
- **API Version Management**: Flexible API version configuration for different environments
- **Parameter Strategies**: Configurable parameter recycling for sustained testing
- **Iteration Timing**: Custom iteration timing with constant and random intervals
- **Logging Control**: Granular logging configuration with level and output control
- **File Path Management**: Configurable file directories and upload paths

### Examples Added
- Survey processing with MongoDB integration
- Backend prescription processing with database operations
- Frontend prescription processing with complete API workflows
- Recognition engine testing with file uploads and custom assertions
- MongoDB document construction for various scenarios
- Device management and authentication workflows

### Documentation
- Comprehensive README with installation and usage guides
- Module-level documentation with feature explanations
- Code examples for common use cases
- Advanced usage patterns and configuration guides
- API reference documentation with parameter descriptions

---

## Version History Summary

- **v0.1.0**: Initial release with comprehensive NeoX application testing utilities
  - Survey processing task modules with MongoDB integration
  - Prescription processing workflows (backend and frontend)
  - Recognition engine testing framework
  - MongoDB document construction utilities
  - Device management and authentication utilities

---

## Migration Guide

This is the initial release, so no migration is required.

## Support

For questions, issues, or feature requests:
- **Address**: [Bitbucket](https://bitbucket.org/neoxinc/testing/src/main/)
- **Documentation**: See README.md for detailed usage instructions

## Contributors

- KunoLu - Initial development and architecture

---

**Note**: This changelog follows [Keep a Changelog](https://keepachangelog.com/) format for easy tracking of project evolution.