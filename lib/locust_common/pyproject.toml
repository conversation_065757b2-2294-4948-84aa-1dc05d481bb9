[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "locust-common"
version = "0.1.0"
description = "A basic locust function for performance testing"
requires-python = ">=3.10"
dependencies = [
    "aenum>=3.1.15",
    "jmespath>=1.0.1",
    "locust>=2.23.1",
    "loguru>=0.7.2",
    "pymongo>=4.10.1",
    "pyyaml>=6.0.1",
    "orjson>=3.10.7",
    "toml>=0.10.2",
    "websocket-client>=1.8.0",
]
authors = [{ name = "<PERSON><PERSON>", email = "<EMAIL>" }]
readme = { file = "README.md", content-type = "text/markdown" }
license = { text = "MIT License" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/src/main/"
