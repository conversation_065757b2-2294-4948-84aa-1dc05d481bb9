#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-20
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

"""
Parameter Module

This module provides parameter management utilities for Locust testing scenarios,
including data queues, global variables, and configuration mapping.

Components:
    DQueue: Data queue management for parameterized testing
    ExprJmesMap: JMESPath expression mapping for configuration access
    GlobalContext: Cross-module global variable management
    GlobalVariable: Dynamic global variable utilities
"""
