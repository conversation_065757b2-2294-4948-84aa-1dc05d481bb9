#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2022-08-14
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   GlobalContext.py
@Software   :   PyCharm
"""

from typing import Any, Dict, Optional

# Global variable storage
_global_data: Dict[str, Any] = {}


def __init(global_data: Optional[Dict[str, Any]]) -> None:
    """
    Initialize global variable storage.

    Sets up the global data dictionary with initial values. This function
    should be called once during application startup to initialize the
    global context system.

    Args:
        global_data: Initial key-value pairs for global variables,
                    defaults to empty dictionary if None

    Example:
        >>> __init({"app_name": "MyApp", "version": "1.0"})
        >>> print(show_all())  # {"app_name": "MyApp", "version": "1.0"}

    Note:
        This function modifies the global _global_data variable and should
        be called before any other global context operations.
    """
    global _global_data
    _global_data = global_data if global_data else {}


def show_all(default: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
    """
    Retrieve all global variables.

    Returns the complete global data dictionary containing all stored
    key-value pairs. If the global data is not accessible, returns
    the default value.

    Args:
        default: Default value to return if global data is inaccessible

    Returns:
        Dictionary containing all global variables, or default value

    Example:
        >>> add_kv("user", "john")
        >>> add_kv("role", "admin")
        >>> all_data = show_all()
        >>> print(all_data)  # {"user": "john", "role": "admin"}
    """
    try:
        return _global_data
    except Exception:
        return default


def add_kv(key: str, value: Any) -> None:
    """
    Add or update a key-value pair in global context.

    Stores a new key-value pair in the global data dictionary or updates
    an existing key with a new value.

    Args:
        key: String key for the global variable
        value: Value to associate with the key (can be any type)

    Example:
        >>> add_kv("database_url", "postgresql://localhost/mydb")
        >>> add_kv("max_connections", 100)
        >>> add_kv("features", ["auth", "logging", "monitoring"])
    """
    _global_data[key] = value


def get_value(key: str, default: Optional[Any] = None) -> Optional[Any]:
    """
    Retrieve the value of a specific global variable.

    Gets the value associated with the specified key from the global
    data dictionary. Returns the default value if the key doesn't exist.

    Args:
        key: String key of the global variable to retrieve
        default: Default value to return if key is not found

    Returns:
        Value associated with the key, or default value if key not found

    Example:
        >>> add_kv("timeout", 30)
        >>> timeout = get_value("timeout")
        >>> print(timeout)  # 30
        >>>
        >>> missing = get_value("nonexistent", "fallback")
        >>> print(missing)  # "fallback"
    """
    try:
        return _global_data[key]
    except KeyError:
        return default


def rm_key(key: str, default: Optional[Any] = None) -> Optional[Any]:
    """
    Remove a key from global context and return its value.

    Removes the specified key from the global data dictionary and returns
    the value that was associated with it. Returns the default value if
    the key doesn't exist.

    Args:
        key: String key of the global variable to remove
        default: Default value to return if key is not found

    Returns:
        Value that was associated with the removed key, or default value

    Example:
        >>> add_kv("temp_data", "temporary")
        >>> removed_value = rm_key("temp_data")
        >>> print(removed_value)  # "temporary"
        >>> print(get_value("temp_data"))  # None
    """
    try:
        return _global_data.pop(key)
    except KeyError:
        return default


def clear_all() -> None:
    """
    Clear all global variables.

    Removes all key-value pairs from the global data dictionary,
    effectively resetting the global context to an empty state.

    Example:
        >>> add_kv("key1", "value1")
        >>> add_kv("key2", "value2")
        >>> print(len(show_all()))  # 2
        >>> clear_all()
        >>> print(len(show_all()))  # 0

    Note:
        This operation is irreversible. All stored global variables
        will be permanently lost.
    """
    _global_data.clear()


# Usage Instructions:
#
# 1. In __init__.py: from locust_common import GlobalContext as gbc
# 2. In __init__.py: Call __init() method to initialize the global context
# 3. In other *.py files: from locust_common import GlobalContext as gbc
# 4. Use gbc methods (show_all/add_kv/get_value/rm_key/clear_all) to manage
#    cross-module global variables for create, read, update, and delete operations.
