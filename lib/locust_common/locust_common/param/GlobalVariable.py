#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2022-08-13
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   GlobalVariable.py
@Software   :   PyCharm
"""

from typing import Any, Iterable, List, TypeVar, Union

# Type alias for name parameter
NameType = TypeVar("NameType", str, Iterable[str])


class GlobalVariable:
    """
    Dynamic global variable management class.

    This class provides a flexible way to manage global variables with
    dynamic attribute creation, retrieval, and deletion capabilities.
    It supports both single variable operations and batch operations
    on multiple variables.
    """

    def __init__(self, **kwargs) -> None:
        """
        Initialize GlobalVariable instance.

        Args:
            **kwargs: Initial key-value pairs to set as attributes

        Example:
            >>> gv = GlobalVariable(app_name="MyApp", version="1.0")
            >>> print(gv.app_name)  # "MyApp"
        """
        # 使用 kwargs 设置初始属性
        for key, value in kwargs.items():
            setattr(self, key, value)

    def all(self) -> List[str]:
        """
        Get all attribute names excluding internal methods and special attributes.

        Returns a list of all user-defined attribute names, filtering out
        Python internal attributes and the class's built-in methods.

        Returns:
            List of attribute names as strings

        Example:
            >>> gv = GlobalVariable()
            >>> gv.username = "john"
            >>> gv.role = "admin"
            >>> print(gv.all())  # ["username", "role"]
        """
        attrs = [i for i in dir(self)]
        attr = []

        for i in range(len(attrs)):
            # 过滤掉内部属性（以双下划线开头）
            if len(attrs[i]) > 1 and attrs[i][:2] == "__":
                continue
            # 过滤掉类方法
            if attrs[i] in ["all", "add", "rm", "get"]:
                continue
            attr.append(attrs[i])

        return attr

    def add(self, name: Union[str, Iterable[str]], value: Any) -> None:
        """
        Add one or multiple attributes dynamically.

        Adds new attributes to the instance. Supports both single attribute
        addition and batch addition of multiple attributes.

        Args:
            name: Single attribute name or iterable of attribute names
            value: Single value or iterable of values (must match name structure)

        Raises:
            TypeError: If name is iterable but value is not iterable

        Example:
            >>> gv = GlobalVariable()
            >>> # Add single attribute
            >>> gv.add("database_url", "postgresql://localhost/db")
            >>>
            >>> # Add multiple attributes
            >>> gv.add(["host", "port", "ssl"], ["localhost", 5432, True])
        """
        # 处理类型匹配检查
        if isinstance(name, Iterable) and not isinstance(name, str):
            if not isinstance(value, Iterable) or isinstance(value, str):
                raise TypeError("value should be Iterable when name is Iterable")

        # 标准化为列表格式
        if isinstance(name, str):
            name = [name]
            value = [value]

        # 动态设置属性
        for n, v in zip(name, value):
            setattr(self, n, v)

    def rm(self, name: Union[str, Iterable[str]]) -> None:
        """
        Remove one or multiple attributes.

        Removes specified attributes from the instance. Supports both
        single attribute removal and batch removal of multiple attributes.

        Args:
            name: Single attribute name or iterable of attribute names

        Example:
            >>> gv = GlobalVariable()
            >>> gv.temp_var = "temporary"
            >>> gv.another_temp = "also temporary"
            >>>
            >>> # Remove single attribute
            >>> gv.rm("temp_var")
            >>>
            >>> # Remove multiple attributes
            >>> gv.rm(["another_temp", "nonexistent"])  # Safe even if attr doesn't exist
        """
        # 标准化为列表格式
        if isinstance(name, str):
            name = [name]

        # 删除指定属性
        for n in name:
            try:
                delattr(self, n)
            except AttributeError:
                # 忽略不存在的属性
                pass

    def get(self, name: Union[str, Iterable[str]]) -> Union[Any, List[Any]]:
        """
        Get one or multiple attribute values.

        Retrieves the values of specified attributes. Returns a single value
        for single attribute access, or a list of values for multiple attributes.

        Args:
            name: Single attribute name or iterable of attribute names

        Returns:
            Single attribute value or list of attribute values

        Raises:
            AttributeError: If a requested attribute does not exist

        Example:
            >>> gv = GlobalVariable()
            >>> gv.username = "john"
            >>> gv.role = "admin"
            >>>
            >>> # Get single attribute
            >>> user = gv.get("username")
            >>> print(user)  # "john"
            >>>
            >>> # Get multiple attributes
            >>> info = gv.get(["username", "role"])
            >>> print(info)  # ["john", "admin"]
        """
        if isinstance(name, str):
            return getattr(self, name)
        else:
            attrs = []
            for n in name:
                attrs.append(getattr(self, n))
            return attrs


# Global instance initialization with error handling
try:
    # 检查全局变量是否已存在
    globalVariables.all()  # type: ignore
except NameError:
    # 如果不存在则创建新实例
    globalVariables = GlobalVariable()


# Usage Example:
#
# from locust_common.param.GlobalVariable import globalVariables as gbv
#
# # Add single variable
# gbv.a = 199
# gbv.b = 200
#
# # Add multiple variables
# nameList = ['x', 'y', 'z']
# valueList = [100, 200, 300]
# gbv.add(nameList, valueList)
#
# # Get all attribute names
# print(gbv.all())  # ['a', 'b', 'x', 'y', 'z']
#
# # Get specific attribute values
# print(gbv.get(nameList))  # [100, 200, 300]
#
# # Remove attributes
# gbv.rm('a')
#
# # Get remaining attribute names
# print(gbv.all())  # ['b', 'x', 'y', 'z']
#
# # Get single attribute value
# print(gbv.get('z'))  # 300
