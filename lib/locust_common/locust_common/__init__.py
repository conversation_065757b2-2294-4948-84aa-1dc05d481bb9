#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-12-21
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

"""
Locust Common Package

This package provides common utilities and frameworks for Locust performance testing,
including task frameworks, parameter handling, configuration management, and logging utilities.

Modules:
    frame: Core framework classes for HTTP requests, WebSocket, and MongoDB testing
    function: Utility functions for configuration, logging, and parameter handling
    param: Parameter management and global variable utilities
"""
