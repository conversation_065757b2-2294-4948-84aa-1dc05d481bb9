#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-20
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

"""
Frame Module

This module contains framework classes for different types of testing scenarios
in Locust performance testing environments.

Components:
    TaskFrame: HTTP request handling framework with comprehensive logging and assertion
    WSFrame: WebSocket communication framework for real-time testing
    MongoDB: MongoDB database testing framework for database performance evaluation
"""
