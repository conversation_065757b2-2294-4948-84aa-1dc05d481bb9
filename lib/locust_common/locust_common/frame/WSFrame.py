#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2023-03-30
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   WSFrame.py
@Software   :   PyCharm
"""

import time
from typing import Any, Dict, Optional, Type, Union

import gevent
import websocket
from locust import User
from loguru._logger import Logger

# Constants
REQUEST_TYPE_WS_RECV = "WS_Recv"
REQUEST_TYPE_WS_SEND = "WS_Send"
ACTION_RECV_MSG = "RecvMsg"
ACTION_SEND_MSG = "SendMsg"
DEFAULT_LOG_LEVEL = "DEBUG"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {thread} | {message}"
LOG_ROTATION = "10MB"
LOG_RETENTION = 4
HEARTBEAT_INTERVAL = 15


class WebSocketClient:
    """
    WebSocket client framework for Locust performance testing.

    This class provides WebSocket communication capabilities with Locust-compatible
    event tracking, logging, and performance metrics. It supports connection management,
    message sending/receiving, and heartbeat functionality.
    """

    _locust_environment = None

    def __init__(self):
        """
        Initialize WebSocket client with default settings.

        Sets up the client with empty events and websocket connection objects.
        """
        self.events = None
        self.ws = None

    def __del__(self):
        """
        Clean up WebSocket connection when object is destroyed.

        Ensures proper resource cleanup by closing the WebSocket connection.
        """
        if self.ws:
            self.ws.close()

    def _req_failure(
        self, action: str, start_time: float, message: Union[str, bytes]
    ) -> None:
        """
        Track failed WebSocket request with Locust events.

        Args:
            action: Action name for the failed request
            start_time: Request start timestamp
            message: Error message or response content
        """
        # 统计失败请求并触发 Locust 事件
        if self.events:
            message_str = message.decode() if isinstance(message, bytes) else message
            self.events.request.fire(
                request_type=REQUEST_TYPE_WS_RECV,
                name=action,
                response_time=int((time.time() - start_time) * 1000),
                response_length=len(message),
                exception=f"[ {action} ] error message is: [ {message_str} ]",
            )

    def _req_success(
        self, action: str, start_time: float, message: Union[str, bytes]
    ) -> None:
        """
        Track successful WebSocket request with Locust events.

        Args:
            action: Action name for the successful request
            start_time: Request start timestamp
            message: Response message content
        """
        # 统计成功请求并触发 Locust 事件
        if self.events:
            self.events.request.fire(
                request_type=REQUEST_TYPE_WS_RECV,
                name=action,
                response_time=int((time.time() - start_time) * 1000),
                response_length=len(message),
            )

    def _req_fire(
        self, name: str, start_time: float, message: str, context: Dict[str, Any]
    ) -> None:
        """
        Track WebSocket send request with Locust events.

        Args:
            name: Request name for tracking
            start_time: Request start timestamp
            message: Message content being sent
            context: Additional context information
        """
        # 统计发送请求并触发 Locust 事件
        if self.events:
            self.events.request.fire(
                request_type=REQUEST_TYPE_WS_SEND,
                name=name,
                response_time=int((time.time() - start_time) * 1000),
                response_length=len(message),
                response=message,
                context=context,
                exception=None,
            )

    @staticmethod
    def define_log_handler(
        locust_logger: Type[Logger],
        log_enable: bool = False,
        log_level: str = DEFAULT_LOG_LEVEL,
    ) -> tuple[Type[Logger], bool]:
        """
        Configure logging handler for WebSocket operations.

        Sets up log file rotation, formatting, and other logging parameters
        specifically for WebSocket testing scenarios.

        Args:
            locust_logger: Logger instance to configure
            log_enable: Whether to enable logging
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)

        Returns:
            Tuple of configured logger and enable flag
        """
        if log_enable:
            # 移除现有的日志处理器并添加新配置
            locust_logger.remove(handler_id=None)  # type: ignore
            locust_logger.add(
                sink="locust_ws_{time:YYYY-MM-DD}.log",
                level=log_level,
                format=LOG_FORMAT,
                rotation=LOG_ROTATION,
                retention=LOG_RETENTION,
                backtrace=True,
                diagnose=True,
                encoding="utf-8",
            )  # type: ignore
        return locust_logger, log_enable

    def connect(
        self,
        host: str,
        header: Optional[Dict[str, str]] = None,
        locust_logger: Optional[Type[Logger]] = None,
        log_enable: bool = False,
    ) -> None:
        """
        Establish WebSocket connection to the specified host.

        Creates WebSocket connection and spawns a receive handler
        for incoming messages.

        Args:
            host: WebSocket server URL
            header: Optional HTTP headers for connection
            locust_logger: Logger instance for connection logging
            log_enable: Whether to enable connection logging
        """
        # 建立 WebSocket 连接
        self.ws = websocket.create_connection(host, header=header)
        # 启动接收消息的协程
        gevent.spawn(self.receive, locust_logger, log_enable)

    def receive(
        self,
        locust_logger: Optional[Type[Logger]] = None,
        log_enable: bool = False,
    ) -> None:
        """
        Receive and process incoming WebSocket messages.

        This method continuously listens for incoming messages
        and tracks them with Locust events.

        Args:
            locust_logger: Logger instance for message logging
            log_enable: Whether to enable message logging
        """
        # 记录接收操作开始时间
        start_time = time.time()
        # 接收 WebSocket 消息
        if self.ws:
            message = self.ws.recv()

            if len(message) > 0:
                # 记录接收到的消息日志
                if log_enable and locust_logger:
                    message_str = (
                        message.decode() if isinstance(message, bytes) else message
                    )
                    locust_logger.debug(f"↓: {message_str}")  # type: ignore

                action = ACTION_RECV_MSG
                # 在此处添加断言条件 (condition)
                condition = True

                # 根据断言结果触发成功或失败事件
                if condition:
                    self._req_success(action, start_time, message)
                else:
                    self._req_failure(action, start_time, message)

    def send(
        self,
        body: str,
        context: Optional[Dict[str, Any]] = None,
        locust_logger: Optional[Type[Logger]] = None,
        log_enable: bool = False,
    ) -> None:
        """
        Send message through WebSocket connection.

        Sends a message to the WebSocket server and tracks
        the operation with Locust events.

        Args:
            body: Message content to send
            context: Optional context information
            locust_logger: Logger instance for message logging
            log_enable: Whether to enable message logging
        """
        if context is None:
            context = {}

        action = ACTION_SEND_MSG

        # 记录发送的消息日志
        if log_enable and locust_logger:
            locust_logger.debug(f"↑: {body}")  # type: ignore

        # 记录发送操作开始时间
        start_time = time.time()

        # 触发发送请求事件
        self._req_fire(
            name=action, start_time=start_time, message=body, context=context
        )

        # 发送 WebSocket 消息
        if self.ws:
            self.ws.send(body)

    def sleep_with_heartbeat(
        self,
        body: str,
        seconds: int,
        locust_logger: Optional[Type[Logger]] = None,
        log_enable: bool = False,
    ) -> None:
        """
        Simulate heartbeat messages during sleep periods.

        Sends periodic heartbeat messages while waiting, useful for
        maintaining WebSocket connections during idle periods.

        Args:
            body: Heartbeat message content
            seconds: Total sleep duration in seconds
            locust_logger: Logger instance for heartbeat logging
            log_enable: Whether to enable heartbeat logging
        """
        # 模拟心跳包发送
        while seconds >= 0:
            # 休眠最多 15 秒或剩余时间
            gevent.sleep(min(HEARTBEAT_INTERVAL, seconds))
            seconds -= HEARTBEAT_INTERVAL

            # 发送心跳消息
            self.send(body=body, locust_logger=locust_logger, log_enable=log_enable)

            # 启动接收消息的协程
            gevent.spawn(self.receive, locust_logger, log_enable)


class WebSocketUser(User):
    """
    WebSocket user class for Locust testing scenarios.

    This abstract user class provides WebSocket testing capabilities
    with automatic client initialization and event binding.
    """

    abstract = True

    def __init__(self, *args, **kwargs):
        """
        Initialize WebSocket user with client and environment setup.

        Sets up the WebSocket client and binds it to the Locust
        environment for event tracking.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        # 初始化 WebSocket 客户端
        self.client = WebSocketClient()
        # 绑定 Locust 环境和事件
        self.client._locust_environment = self.environment
        self.client.events = self.environment.events
