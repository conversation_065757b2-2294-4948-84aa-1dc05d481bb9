#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2022-02-09
<AUTHOR>   Ku<PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   func_log.py
@Software   :   PyCharm
"""

from pathlib import Path
from typing import Optional, Type, Union

from loguru._logger import Logger

# Constants
DEFAULT_LOG_LEVEL = "INFO"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {thread} | {message}"
LOG_ROTATION = "10MB"
LOG_RETENTION = 4
DEFAULT_LOG_FILENAME = "internal_{time:YYYY-MM-DD}.log"


def set_log(
    flag: bool,
    clog: Type[Logger],
    log_level: Optional[str] = DEFAULT_LOG_LEVEL,
    log_file: Optional[Path] = None,
) -> Union[Type[Logger], None]:
    """
    Configure logging settings for internal operations.

    Sets up log file rotation, formatting, and other logging parameters
    based on the provided configuration. When logging is enabled, it removes
    existing handlers and adds a new file handler with specified settings.

    Args:
        flag: Whether to enable logging functionality
        clog: Loguru logger instance to configure
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Directory path for log file generation (defaults to current working directory)

    Returns:
        Configured logger instance if flag is True, None otherwise

    Example:
        >>> from loguru import logger
        >>> from pathlib import Path
        >>> log_dir = Path("/var/log/myapp")
        >>> configured_logger = set_log(True, logger, "DEBUG", log_dir)
        >>> if configured_logger:
        ...     configured_logger.info("Logging is now configured")
    """
    # 设置默认日志文件路径
    if log_file is None:
        log_file = Path.cwd()

    # 构建完整的日志文件路径
    log_file_path = log_file.joinpath(DEFAULT_LOG_FILENAME)

    if flag:
        # 移除所有现有的日志处理器
        clog.remove(handler_id=None)  # type: ignore

        # 添加新的文件日志处理器
        clog.add(
            sink=str(log_file_path),
            level=log_level,
            format=LOG_FORMAT,
            rotation=LOG_ROTATION,
            retention=LOG_RETENTION,
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
        )  # type: ignore
        return clog

    # 如果未启用日志，返回 None
    return None
