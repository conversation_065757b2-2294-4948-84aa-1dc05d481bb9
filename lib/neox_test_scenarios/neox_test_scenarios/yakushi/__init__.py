# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import Page Object classes for internal use within yakushi package
try:
    from .pages.login.login_page import LoginPage

    _LOGIN_PAGE_AVAILABLE = True
except ImportError:
    LoginPage = None
    _LOGIN_PAGE_AVAILABLE = False

try:
    from .pages.main.main_page import MainPage

    _MAIN_PAGE_AVAILABLE = True
except ImportError:
    MainPage = None
    _MAIN_PAGE_AVAILABLE = False

try:
    from .pages.base.base_page import BasePage

    _BASE_PAGE_AVAILABLE = True
except ImportError:
    BasePage = None
    _BASE_PAGE_AVAILABLE = False
