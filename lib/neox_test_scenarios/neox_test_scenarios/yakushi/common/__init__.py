#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yakushi Common Utilities Module

This module provides common utilities and helper functions for Yakushi
application testing. It includes functions for desktop management, application
startup, window handling, and user interaction automation.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import all common utilities for Yakushi testing
from .common import (
    com_click_confirm_btn,
    com_click_login_btn,
    com_get_yakushi_window,
    com_open_homepage_from_float_window,
    com_open_yakushi_app,
    com_show_desktop,
    com_write_acc_info,
)

# Define public API
__all__ = [
    "com_show_desktop",  # Desktop management
    "com_open_yakushi_app",  # Application startup
    "com_write_acc_info",  # Account information input
    "com_click_login_btn",  # Login button interaction
    "com_click_confirm_btn",  # Confirm button interaction
    "com_get_yakushi_window",  # Window retrieval
    "com_open_homepage_from_float_window",  # Homepage navigation
]
