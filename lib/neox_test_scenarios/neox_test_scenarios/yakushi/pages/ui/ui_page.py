#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI Page Object

This module provides the Page Object implementation for Yakushi UI validation functionality.
It encapsulates all UI validation related operations including floating window, homepage window,
and popup window validations.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   ui_page.py
@Software   :   PyCharm
"""

import time
from typing import Any, Dict, Optional

import allure
import uiautomation as ui
from neox_test_common import logger

from ..base.base_page import BasePage


class UIPage(BasePage):
    """
    Yakushi UI验证页面的Page Object类

    该类封装了UI验证相关的所有操作，包括浮动窗口验证、
    主页窗口验证、弹出窗口验证等功能。

    继承自BasePage，具有基础页面的所有通用功能。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化UI验证页面

        Args:
            config (dict): 测试配置数据，包含yakushi.toml的配置信息
        """
        super().__init__(config)

    def get_float_window(self) -> Optional[ui.WindowControl]:
        """
        获取浮动窗口控件

        Returns:
            WindowControl: 浮动窗口控件，如果未找到则返回None
        """
        float_window_auto_id = self.get_element_config(
            "yakushi.modules.common.window.float.auto_id"
        )
        return self.find_window(auto_id=float_window_auto_id)

    def get_main_window(self) -> Optional[ui.WindowControl]:
        """
        获取主窗口控件

        Returns:
            WindowControl: 主窗口控件，如果未找到则返回None
        """
        main_window_auto_id = self.get_element_config(
            "yakushi.modules.common.window.main.auto_id"
        )
        return self.find_window(auto_id=main_window_auto_id)

    def validate_floating_window(self) -> bool:
        """
        验证浮动窗口的UI元素和功能

        Returns:
            bool: 验证是否成功
        """
        try:
            with allure.step("验证浮动窗口UI"):
                float_window = self.get_float_window()
                if not float_window:
                    logger.error("浮动窗口未找到")
                    return False

                # 验证浮动窗口是否可见
                if not float_window.Exists(timeout=3):
                    logger.error("浮动窗口不可见")
                    return False

                # 验证浮动窗口的基本属性
                window_rect = float_window.BoundingRectangle
                logger.info(f"浮动窗口位置和大小: {window_rect}")

                # 验证浮动窗口中的按钮
                main_button_auto_id = self.get_element_config(
                    "yakushi.modules.common.button.main.auto_id"
                )
                if main_button_auto_id:
                    main_button = float_window.ButtonControl(
                        AutomationId=main_button_auto_id
                    )
                    if main_button.Exists(timeout=2):
                        logger.info("浮动窗口中的主窗口按钮验证成功")
                    else:
                        logger.warning("浮动窗口中的主窗口按钮未找到")

                logo_button_auto_id = self.get_element_config(
                    "yakushi.modules.common.button.logo.auto_id"
                )
                if logo_button_auto_id:
                    logo_button = float_window.ButtonControl(
                        AutomationId=logo_button_auto_id
                    )
                    if logo_button.Exists(timeout=2):
                        logger.info("浮动窗口中的Logo按钮验证成功")
                    else:
                        logger.warning("浮动窗口中的Logo按钮未找到")

                logger.info("浮动窗口UI验证成功")
                return True

        except Exception as e:
            logger.error(f"验证浮动窗口UI失败: {e}")
            return False

    def validate_homepage_window(self) -> bool:
        """
        验证主页窗口的UI元素和功能

        Returns:
            bool: 验证是否成功
        """
        try:
            with allure.step("验证主页窗口UI"):
                main_window = self.get_main_window()
                if not main_window:
                    logger.error("主页窗口未找到")
                    return False

                # 验证主页窗口是否可见
                if not main_window.Exists(timeout=3):
                    logger.error("主页窗口不可见")
                    return False

                # 验证主页窗口的基本属性
                window_rect = main_window.BoundingRectangle
                logger.info(f"主页窗口位置和大小: {window_rect}")

                # 验证窗口标题
                window_name = main_window.Name
                if window_name:
                    logger.info(f"主页窗口标题: {window_name}")

                # 验证主页窗口中的主要UI元素
                # 这里可以添加更多具体的UI元素验证
                logger.info("主页窗口UI验证成功")
                return True

        except Exception as e:
            logger.error(f"验证主页窗口UI失败: {e}")
            return False

    def validate_popup_window(self) -> bool:
        """
        验证弹出窗口的UI元素和功能

        Returns:
            bool: 验证是否成功
        """
        try:
            with allure.step("验证弹出窗口UI"):
                # 查找可能的弹出窗口
                # 弹出窗口通常是Dialog或者临时的Window
                popup_windows = []

                # 查找Dialog控件
                try:
                    for dialog in ui.GetRootControl().GetChildren():
                        if dialog.ControlType == ui.ControlType.WindowControl:
                            if dialog.ClassName in ["#32770", "Dialog", "PopupWindow"]:
                                popup_windows.append(dialog)
                except Exception:
                    pass

                if not popup_windows:
                    logger.info("当前没有弹出窗口")
                    return True

                # 验证找到的弹出窗口
                for i, popup in enumerate(popup_windows):
                    logger.info(f"验证弹出窗口 {i + 1}")

                    # 验证弹出窗口的基本属性
                    if popup.Exists(timeout=1):
                        window_rect = popup.BoundingRectangle
                        window_name = popup.Name
                        logger.info(
                            f"弹出窗口 {i + 1} - 标题: {window_name}, 位置和大小: {window_rect}"
                        )

                logger.info("弹出窗口UI验证成功")
                return True

        except Exception as e:
            logger.error(f"验证弹出窗口UI失败: {e}")
            return False

    def validate_window_switching(self) -> bool:
        """
        验证窗口切换功能

        Returns:
            bool: 验证是否成功
        """
        try:
            with allure.step("验证窗口切换功能"):
                # 首先确保浮动窗口存在
                float_window = self.get_float_window()
                if not float_window:
                    logger.error("浮动窗口未找到，无法测试窗口切换")
                    return False

                # 点击主窗口按钮
                main_button_auto_id = self.get_element_config(
                    "yakushi.modules.common.button.main.auto_id"
                )
                if main_button_auto_id:
                    main_button = float_window.ButtonControl(
                        AutomationId=main_button_auto_id
                    )
                    if main_button.Exists(timeout=2):
                        main_button.Click()
                        time.sleep(1)  # 等待窗口切换

                        # 验证主窗口是否显示
                        main_window = self.get_main_window()
                        if main_window and main_window.Exists(timeout=3):
                            logger.info("窗口切换功能验证成功")
                            return True
                        else:
                            logger.error("点击主窗口按钮后，主窗口未显示")
                            return False
                    else:
                        logger.error("主窗口按钮未找到")
                        return False
                else:
                    logger.error("主窗口按钮配置未找到")
                    return False

        except Exception as e:
            logger.error(f"验证窗口切换功能失败: {e}")
            return False

    def validate_all_ui_elements(self) -> bool:
        """
        验证所有UI元素

        Returns:
            bool: 验证是否成功
        """
        try:
            with allure.step("验证所有UI元素"):
                results = []

                # 验证浮动窗口
                results.append(self.validate_floating_window())

                # 验证主页窗口
                results.append(self.validate_homepage_window())

                # 验证弹出窗口
                results.append(self.validate_popup_window())

                # 验证窗口切换功能
                results.append(self.validate_window_switching())

                # 检查所有验证结果
                success_count = sum(results)
                total_count = len(results)

                logger.info(f"UI验证完成: {success_count}/{total_count} 项验证成功")

                return all(results)

        except Exception as e:
            logger.error(f"验证所有UI元素失败: {e}")
            return False
