#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NeoX Test Scenarios Library

This package provides test scenarios and business logic implementations for the
NeoX testing framework, specifically focused on Yakushi application testing.

The library includes common utilities, login/logout workflows, and various
functional test scenarios for comprehensive application testing.

@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

from .yakushi.common import (
    com_click_confirm_btn,
    com_click_login_btn,
    com_get_yakushi_window,
    com_open_homepage_from_float_window,
    com_open_yakushi_app,
    com_show_desktop,
    com_write_acc_info,
)
from .yakushi.login_logout.login_business import (
    perform_login_flow,
    verify_login_failure,
    verify_login_success,
)
from .yakushi.login_logout.logout_business import close_application, perform_logout_flow

# Define public API
__all__ = [
    # Common utilities
    "com_show_desktop",
    "com_open_yakushi_app",
    "com_write_acc_info",
    "com_click_login_btn",
    "com_click_confirm_btn",
    "com_get_yakushi_window",
    "com_open_homepage_from_float_window",
    # Login/logout workflows
    "perform_login_flow",
    "verify_login_success",
    "verify_login_failure",
    "perform_logout_flow",
    "close_application",
]
