#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
POM Usage Example

This script demonstrates how to use the new Page Object Model classes
for Yakushi UI automation testing.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   pom_usage_example.py
@Software   :   PyCharm
"""

import os
import sys
import toml

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

from neox_test_common import logger

def load_config():
    """Load yakushi configuration"""
    config_path = os.path.join(
        project_root,
        "Automation",
        "Windows", 
        "KENTA",
        "conf",
        "yakushi.toml"
    )
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            return toml.load(f)
    else:
        logger.error(f"Config file not found: {config_path}")
        return None

def example_login_workflow():
    """Example: Complete login workflow using LoginPage"""
    logger.info("=== Login Workflow Example ===")
    
    try:
        from neox_test_scenarios.yakushi.pages import LoginPage
        
        config = load_config()
        if not config:
            return False
        
        # Create LoginPage instance
        login_page = LoginPage(config)
        
        # Example 1: Perform complete login flow
        logger.info("Performing complete login flow...")
        # Note: This would actually interact with the UI in a real test
        # login_page.perform_complete_login_flow(use_correct_credentials=True)
        # login_page.verify_login_success()
        
        # Example 2: Individual operations
        logger.info("Example of individual login operations...")
        # username = login_page.get_element_config("yakushi.modules.login.account")
        # password = login_page.get_element_config("yakushi.modules.login.password")
        # logger.info(f"Login credentials configured: {username}")
        
        logger.info("✓ LoginPage example completed successfully")
        return True
        
    except ImportError as e:
        logger.error(f"LoginPage not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in login workflow example: {e}")
        return False

def example_main_page_operations():
    """Example: Main page operations using MainPage"""
    logger.info("=== Main Page Operations Example ===")
    
    try:
        from neox_test_scenarios.yakushi.pages import MainPage
        
        config = load_config()
        if not config:
            return False
        
        # Create MainPage instance
        main_page = MainPage(config)
        
        # Example operations
        logger.info("Example main page operations...")
        # main_page.verify_main_page_loaded()
        # main_page.open_main_window_from_float()
        # main_page.toggle_window_size()
        
        logger.info("✓ MainPage example completed successfully")
        return True
        
    except ImportError as e:
        logger.error(f"MainPage not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in main page example: {e}")
        return False

def example_prescription_history_operations():
    """Example: Prescription history operations"""
    logger.info("=== Prescription History Operations Example ===")
    
    try:
        from neox_test_scenarios.yakushi.pages import PrescriptionHistoryPage
        
        config = load_config()
        if not config:
            return False
        
        # Create PrescriptionHistoryPage instance
        prescription_page = PrescriptionHistoryPage(config)
        
        # Example operations
        logger.info("Example prescription history operations...")
        # prescription_page.verify_prescription_history_page_loaded()
        # prescription_page.display_prescription_detail(0)
        # prescription_page.filter_by_text_items({"patient_name": "Test Patient"})
        # prescription_page.toggle_paging_column()
        
        logger.info("✓ PrescriptionHistoryPage example completed successfully")
        return True
        
    except ImportError as e:
        logger.error(f"PrescriptionHistoryPage not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in prescription history example: {e}")
        return False

def example_backward_compatibility():
    """Example: Backward compatibility with existing functions"""
    logger.info("=== Backward Compatibility Example ===")
    
    try:
        # Import existing functions - these should work exactly as before
        from neox_test_scenarios import (
            perform_login_flow,
            verify_login_success,
            verify_login_failure
        )
        
        config = load_config()
        if not config:
            return False
        
        logger.info("Existing functions imported successfully")
        logger.info("These functions now use Page Object classes internally")
        logger.info("but maintain the same external interface")
        
        # Example usage (commented out to avoid actual UI interaction)
        # perform_login_flow(config, use_correct_credentials=True)
        # verify_login_success(config)
        
        logger.info("✓ Backward compatibility verified")
        return True
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in backward compatibility example: {e}")
        return False

def example_configuration_access():
    """Example: Configuration access through Page Objects"""
    logger.info("=== Configuration Access Example ===")
    
    try:
        from neox_test_scenarios.yakushi.pages import BasePage
        
        config = load_config()
        if not config:
            return False
        
        # Create BasePage instance
        base_page = BasePage(config)
        
        # Example configuration access
        logger.info("Accessing configuration through Page Object...")
        
        # Get various configuration values
        login_account = base_page.get_element_config("yakushi.modules.login.account")
        main_window_id = base_page.get_element_config("yakushi.modules.common.window.main.auto_id")
        float_window_id = base_page.get_element_config("yakushi.modules.common.window.float.auto_id")
        
        logger.info(f"Login account: {login_account}")
        logger.info(f"Main window ID: {main_window_id}")
        logger.info(f"Float window ID: {float_window_id}")
        
        logger.info("✓ Configuration access example completed successfully")
        return True
        
    except ImportError as e:
        logger.error(f"BasePage not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in configuration access example: {e}")
        return False

def main():
    """Main example function"""
    logger.info("Starting POM Usage Examples...")
    
    examples = [
        ("Configuration Access", example_configuration_access),
        ("Login Workflow", example_login_workflow),
        ("Main Page Operations", example_main_page_operations),
        ("Prescription History Operations", example_prescription_history_operations),
        ("Backward Compatibility", example_backward_compatibility),
    ]
    
    passed = 0
    total = len(examples)
    
    for example_name, example_func in examples:
        logger.info(f"\n--- Running: {example_name} ---")
        try:
            if example_func():
                logger.info(f"✓ PASSED: {example_name}")
                passed += 1
            else:
                logger.error(f"✗ FAILED: {example_name}")
        except Exception as e:
            logger.error(f"✗ ERROR in {example_name}: {e}")
    
    logger.info(f"\n=== Example Results ===")
    logger.info(f"Passed: {passed}/{total}")
    logger.info(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All examples completed successfully!")
        return True
    else:
        logger.error("❌ Some examples failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
