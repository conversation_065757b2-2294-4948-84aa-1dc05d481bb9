[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "NeoXHelper"
version = "0.1.0"
authors = [{ name = "KunoLu", email = "<EMAIL>" }]
description = "A CLI tools which dealing with some requirements on Windows | Web | NeoX production."
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "fire==0.5.0",
    "jmespath==1.0.1",
    "loguru==0.7.2",
    "openpyxl==3.1.5",
    "petl==1.7.15",
    "pyecharts==2.0.6",
    "pyinstaller==6.10.0",
    "pywin32==306",
    "rich==13.7.0",
    "toml==0.10.2",
]

[project.optional-dependencies]
dev = ["pipdeptree"]

[project.urls]
"Homepage" = "https://bitbucket.org/neoxinc/testing/src/main/Tools/NeoXHelper"

[project.scripts]
neox = "neox:main"
