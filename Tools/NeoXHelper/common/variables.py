# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-01
<AUTHOR>   <PERSON><PERSON><PERSON>u
@Email      :   <EMAIL>
@File       :   variables.py
@Software   :   PyCharm
"""
from dataclasses import dataclass


@dataclass(frozen=True)
class ActionItems:
    """
    Data class for action items.
    """
    generate: str = "generate"
    feed: str = "feed"
    install: str = "install"
    uninstall: str = "uninstall"
    get: str = "get"
    set: str = "set"
    mkdirs: str = "mkdirs"


@dataclass(frozen=True)
class ScopeItems:
    """
    Data class for scope items.
    """
    template: str = "template"
    windows: str = "windows"
    nsips: str = "nsips"
    web: str = None


# @dataclass(frozen=True)
# class IdxItems:
#     """
#     Data class for action scope index.
#     """
#     dpi: int = 0
#     resolution: int = 1
#     prescriptions: int = 0
#     packages: int = 0
#     templates: int = 0
