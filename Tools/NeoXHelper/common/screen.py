# -*- coding: utf-8 -*-
"""
@Date       :   2024/5/17
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   screen
@Software   :   PyCharm
"""
import win32api
import win32con
import win32gui
import win32print
import pywintypes
import time
from pathlib import Path


class Screen:
    """
    Param demo:
    {
        "screen_resolution": [1920, 1080],
        "set_dpi_exe_path": "./SetDpi.exe",
        "dpi": 100,
    }
    """

    def __init__(self, **kwargs):
        self.tar_screen_resolution = kwargs.get('screen_resolution')
        set_dpi_exe_path = kwargs.get('set_dpi_exe_path')
        self.set_dpi_exe = set_dpi_exe_path if set_dpi_exe_path else Path.cwd().joinpath('SetDpi.exe')
        tar_dpi = kwargs.get('dpi')
        self.tar_dpi = tar_dpi if tar_dpi else 100

    def set_screen_resolution(self) -> None:
        """
        Sets the screen resolution to the specified target resolution.
        :return: None
        """
        devmode = pywintypes.DEVMODEType()

        devmode.PelsWidth = self.tar_screen_resolution[0]
        devmode.PelsHeight = self.tar_screen_resolution[1]
        devmode.Fields = win32con.DM_PELSWIDTH | win32con.DM_PELSHEIGHT
        win32api.ChangeDisplaySettings(devmode, 0)

    @staticmethod
    def get_real_resolution() -> tuple[int, int]:
        """
        Get the real resolution of the screen.

        This function retrieves the actual horizontal and vertical resolution of the screen,
        accounting for any scaling or other factors that may affect the displayed resolution.

        :return: A tuple containing the width and height of the screen in pixels.
        """
        hDC = win32gui.GetDC(0)  # Get the device context handle for the desktop
        # Get the horizontal and vertical resolution of the screen
        w = win32print.GetDeviceCaps(hDC, win32con.DESKTOPHORZRES)
        h = win32print.GetDeviceCaps(hDC, win32con.DESKTOPVERTRES)
        return w, h

    @staticmethod
    def get_screen_size() -> tuple[int, int]:
        """
        Get the size of the screen after scaling.

        :return: A tuple containing the width and height of the screen in pixels.
        """
        w = win32api.GetSystemMetrics(0)
        h = win32api.GetSystemMetrics(1)
        return w, h

    @staticmethod
    def get_dpi() -> float:
        """
        Calculates the screen scale rate (DPI) based on the real and screen sizes.

        This function calculates the screen scale rate (DPI) by dividing the real resolution (accounting for any scaling or other factors) by the screen size. The result is then rounded to two decimal places and multiplied by 100 to get the percentage.

        :return: A float representing the screen scale rate (DPI) as a percentage.
        """
        real_resolution = Screen.get_real_resolution()
        screen_size = Screen.get_screen_size()

        screen_scale_rate = round(real_resolution[0] / screen_size[0], 2)
        screen_scale_rate = screen_scale_rate * 100
        return screen_scale_rate

    def set_dpi(self) -> bool:
        """
        Set the screen scale rate (DPI).

        This function runs the executable file specified in set_dpi_exe_path and sets the screen scale rate (DPI) to the value specified in dpi.

        :return: A boolean indicating whether the operation was successful.
        """
        cur_dpi = int(Screen.get_dpi())
        tar_dpi = int(self.tar_dpi)
        print('当前系统缩放率为: ', cur_dpi, '%')
        if cur_dpi == tar_dpi:
            print('当前系统缩放率无需调整！')
            return True
        else:
            try:
                print(f"正在调整为 {tar_dpi}% 缩放率...")
                win32api.ShellExecute(0, 'open', self.set_dpi_exe, f" {tar_dpi}", '', 1)
                print('运行中...')
                time.sleep(3)
                print(f"当前系统缩放率已调整为：{tar_dpi}%")
                return True
            except Exception as ex:
                print(ex)
                return False

    def restore_dpi(self, ori_dpi: int) -> bool:
        """
        Restore the screen scale rate (DPI) to its original value.

        This function runs the executable file specified in set_dpi_exe_path and restores the screen scale rate (DPI) to its original value.

        :param ori_dpi: The original screen scale rate (DPI).
        :return: A boolean indicating whether the operation was successful.
        """
        hinst = win32api.ShellExecute(0, 'open', self.set_dpi_exe, str(ori_dpi), '', 1)
        print(f"HINST: {hinst}")
        return True if hinst > 32 else False
