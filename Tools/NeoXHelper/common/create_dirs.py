# -*- coding: utf-8 -*-
"""
@Time     :   2024/10/21 11:57:29
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON> 
@Email    :   <EMAIL>
@File     :   create_dirs.py
@Software :   Cursor
"""

from pathlib import Path
import random
import string


def _create_random_text_file(directory):
    # 生成随机文件名
    filename = ''.join(random.choices(string.ascii_letters, k=8)) + '.txt'
    file_path = directory / filename

    # 生成随机内容
    content = ''.join(
        random.choices(string.ascii_letters + string.digits, k=100))

    # 写入文件
    with open(file_path, 'w') as file:
        file.write(content)


def create_year_structure(base_path: Path, year: int):
    year_dir = base_path / str(year)

    # 创建年份文件夹
    year_dir.mkdir(exist_ok=True)

    # 创建月和日的文件夹
    for month in range(1, 13):
        month_dir = year_dir / f'{month:02}'
        month_dir.mkdir(exist_ok=True)

        # 根据月份创建日的文件夹
        days_in_month = 31  # 假设每个月都有31天
        if month in [4, 6, 9, 11]:
            days_in_month = 30
        elif month == 2:
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                days_in_month = 29  # 闰年
            else:
                days_in_month = 28

        for day in range(1, days_in_month + 1):
            day_dir = month_dir / f'{day:02}'
            day_dir.mkdir(exist_ok=True)

            # 在日的文件夹下创建随机的有内容的文本文件
            _create_random_text_file(day_dir)


if __name__ == "__main__":
    base_path = Path(input("请输入指定的路径: "))
    year = int(input("请输入年份: "))

    if not base_path.exists():
        print(f"指定的路径 {base_path} 不存在，请检查路径是否正确。")
    else:
        create_year_structure(base_path, year)
        print(f"文件夹结构已成功创建在 {base_path / str(year)} 文件夹下。")
