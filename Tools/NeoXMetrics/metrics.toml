###################################################################
# NeoX Metrics 配置文件
#
# 脚本启动后可验证数据是否写入的地址：http://{http_server_addr}:{http_server_port}/metrics
#       如：http://localhost:7333/metrics
###################################################################

[base]
# 数据采集和写入的时间间隔（秒）
# 控制主循环中每次从 Redis 读取数据并写入 Prometheus 指标的间隔时间
interval_sec = 1

[base.prometheus]
# Prometheus HTTP 服务器监听地址
# "0.0.0.0" 表示监听所有网络接口，允许外部访问
http_server_addr = "0.0.0.0"

# Prometheus HTTP 服务器监听端口
# 用于暴露 /metrics 端点供 Prometheus 抓取数据
http_server_port = 7333

[base.redis]
# Redis 服务器主机地址
# 存储时间序列数据的 Redis 实例地址
redis_host = "localhost"

# Redis 服务器端口
# Redis 实例的监听端口
redis_port = 6379

# Redis 服务器密码
# 如果 Redis 设置了密码认证，在此配置；空字符串表示无密码
redis_password = ""

###################################################################
# Prometheus 指标配置
# 定义要暴露给 Prometheus 的各种指标
###################################################################

# 第一个指标：原始任务状态
[[metrics.gauge]]
# Prometheus 指标名称，用于标识这个指标
name = "kenta_scheduling_status"

# 指标描述信息，会显示在 Prometheus 中
documentation = "Kenta scheduling status in redis TS."

# 指标标签名称列表，用于区分不同维度的数据
# 这里使用 "status" 标签来区分不同的任务状态（new, queued, lock 等）
label_names = ["status"]

# 滑动窗口大小（秒）
# -1 表示不使用滑动窗口，直接获取 Redis TS 的最新值
# 对应代码中的 get_last_time_series_value() 方法
sliding_window_sec = -1

# 第二个指标：60秒聚合的任务状态
[[metrics.gauge]]
# 指标名称
name = "kenta_scheduling_status_transform"

# 指标描述
documentation = "Kenta scheduling status transform from redis TS to Prometheus.(aggregated by 60s)"

# 标签名称
label_names = ["status"]

# 滑动窗口大小：60秒
# 使用60秒滑动窗口计算平均值，对应代码中的 calc_sliding_window_avg_value() 方法
sliding_window_sec = 60

# 第三个指标：自定义时间窗口聚合的任务状态
[[metrics.gauge]]
# 指标名称
name = "kenta_scheduling_status_transform_custom_sec"

# 指标描述
documentation = "Kenta scheduling status transform from redis TS to Prometheus.(aggregated by custom sliding window sec)"

# 标签名称
label_names = ["status"]

# 滑动窗口大小：5秒
# 使用5秒滑动窗口计算平均值，提供更细粒度的数据聚合
sliding_window_sec = 5

# Redis 时间序列键配置
# 所有 gauge 指标共享相同的 Redis TimeSeries 键配置
[metrics.redis]
# 时间序列键映射表
# 每个子数组包含两个元素：[Redis TS 键名, Prometheus 标签值]
# 第一个元素是 Redis 中实际存储数据的时间序列键名
# 第二个元素是在 Prometheus 指标中使用的标签值
ts_keys = [
    [
        "GRAFANA_TASK_STATUS_NEW", # Redis TS 键：新建任务状态计数
        "new",                     # Prometheus 标签值：new
    ],
    [
        "GRAFANA_TASK_STATUS_QUEUED", # Redis TS 键：排队任务状态计数
        "queued",                     # Prometheus 标签值：queued
    ],
    [
        "GRAFANA_TASK_STATUS_LOCK", # Redis TS 键：锁定任务状态计数
        "lock",                     # Prometheus 标签值：lock
    ],
    [
        "GRAFANA_TASK_STATUS_PT", # Redis TS 键：PT状态任务计数
        "pt",                     # Prometheus 标签值：pt
    ],
    [
        "GRAFANA_TASK_STATUS_PB", # Redis TS 键：PB状态任务计数
        "pb",                     # Prometheus 标签值：pb
    ],
    [
        "GRAFANA_TASK_STATUS_MERGE", # Redis TS 键：合并状态任务计数
        "merge",                     # Prometheus 标签值：merge
    ],
    [
        "GRAFANA_TASK_STATUS_TOOK", # Redis TS 键：已完成任务状态计数
        "took",                     # Prometheus 标签值：took
    ],
]

###################################################################
# RTS (Redis TimeSeries) 数据转换配置
# 用于定时任务：每分钟和每小时的数据处理
###################################################################
[metrics.rts]
# 源数据键名
# 每分钟任务会从这个 Redis TS 键读取当前小时的累计数据
# 对应代码中的 get_hourly_sum_from_start() 方法
source_key = "GRAFANA_NEOX_ENGINE_REQUEST_COUNT"

# 目标数据键名
# 每分钟任务会将处理后的数据写入这个 Redis TS 键
# 存储当前小时从整点到现在的累计请求数
target_key = "GRAFANA_SDE_PRES_COUNT_TODAY"

# 对比数据键名
# 每小时任务会将上周同日同时间的数据写入这个键
# 用于周同比分析，对应代码中的 get_last_week_same_hour_last_value() 方法
compare_key = "GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY"
