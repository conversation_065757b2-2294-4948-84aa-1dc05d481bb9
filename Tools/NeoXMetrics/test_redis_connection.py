#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024/12/11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   test_redis_connection.py
@Software   :   PyCharm
"""

from pathlib import Path

import redis
from common.configuration import load_toml


def test_redis_connection():
    """
    测试 Redis 连接和 TimeSeries 模块
    """
    print("测试 Redis 连接...")

    # 读取配置
    config_path = Path("metrics.toml")
    config = load_toml(config_path)

    redis_host = config["base"]["redis"]["redis_host"]
    redis_port = config["base"]["redis"]["redis_port"]
    redis_password = config["base"]["redis"]["redis_password"]

    print(f"连接配置: {redis_host}:{redis_port}")

    try:
        # 连接到 Redis
        r = redis.StrictRedis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=True,
        )

        # 测试基本连接
        print("1. 测试基本连接...")
        pong = r.ping()
        print(f"   PING 响应: {pong}")

        # 测试 TimeSeries 模块
        print("2. 测试 TimeSeries 模块...")
        try:
            # 尝试创建一个测试 TimeSeries
            test_key = "TEST_TS_KEY"
            r.execute_command("TS.CREATE", test_key)
            print(f"   成功创建 TimeSeries: {test_key}")

            # 添加一个数据点
            import time

            timestamp = int(time.time() * 1000)
            r.execute_command("TS.ADD", test_key, timestamp, 100)
            print(f"   成功添加数据点: {timestamp} -> 100")

            # 查询数据
            data = r.execute_command("TS.GET", test_key)
            print(f"   查询结果: {data}")

            # 清理测试数据
            r.delete(test_key)
            print(f"   已清理测试数据: {test_key}")

        except Exception as e:
            print(f"   TimeSeries 模块测试失败: {e}")
            print("   请确保 Redis 服务器已加载 TimeSeries 模块")
            return False

        # 测试配置的 RTS keys
        print("3. 测试配置的 RTS keys...")
        rts_src_key = config["metrics"]["rts"]["source_key"]
        rts_tar_key = config["metrics"]["rts"]["target_key"]
        rts_compare_key = config["metrics"]["rts"]["compare_key"]

        for key_name, key in [
            ("source_key", rts_src_key),
            ("target_key", rts_tar_key),
            ("compare_key", rts_compare_key),
        ]:
            try:
                # 尝试获取 key 信息
                _ = r.execute_command("TS.INFO", key)
                print(f"   {key_name} ({key}): ✅ 已存在")
            except Exception:
                # key 不存在
                print(f"   {key_name} ({key}): ❌ 不存在")
                print(f"      请手动创建: TS.CREATE {key}")

        print("\n✅ Redis 连接和 TimeSeries 模块测试成功！")
        return True

    except Exception as e:
        print(f"\n❌ Redis 连接测试失败: {e}")
        print("\n请检查:")
        print("1. Redis 服务器是否运行")
        print("2. 连接配置是否正确")
        print("3. Redis 是否加载了 TimeSeries 模块")
        return False


if __name__ == "__main__":
    test_redis_connection()
