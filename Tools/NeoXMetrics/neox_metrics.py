# -*- coding: utf-8 -*-
"""
@Date       :   2024/9/6
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   neox_metrics
@Software   :   PyCharm
"""

import logging
import threading
import time
from pathlib import Path
from typing import Union

import pendulum
import redis
from common.configuration import load_toml
from prometheus_client import Gauge, start_http_server


class NeoXMetrics(object):
    def __init__(self, config_filepath: Path):
        """
        初始化实例

        参数：
        config_filepath (Path): 配置文件路径
        """

        # 获取配置文件信息
        config = load_toml(config_filepath)
        # Redis 连接配置
        self.redis_host = config["base"]["redis"]["redis_host"]
        self.redis_port = config["base"]["redis"]["redis_port"]
        self.redis_password = config["base"]["redis"]["redis_password"]
        # 以暴露指标的 HTTP 服务器配置
        self.http_server_port = config["base"]["prometheus"]["http_server_port"]
        self.http_server_addr = config["base"]["prometheus"]["http_server_addr"]
        # 写入数据的迭代时间（秒）
        self.interval = config["base"]["interval_sec"]
        # 滑动窗口大小（秒）
        self.sliding_window_sizes = [
            gauge["sliding_window_sec"] for gauge in config["metrics"]["gauge"]
        ]

        # 要监控的 Redis TS keys - 所有 gauge 共享相同的 ts_keys
        shared_ts_keys = config["metrics"]["redis"]["ts_keys"]
        self.redis_ts_keys = [shared_ts_keys for _ in config["metrics"]["gauge"]]

        # Prometheus 指标
        self.gauges = [
            Gauge(
                name=gauge["name"],
                documentation=gauge["documentation"],
                labelnames=gauge["label_names"],
            )
            for gauge in config["metrics"]["gauge"]
        ]

        # 要做数据转换的 Redis TS keys
        self.rts_src_key = config["metrics"]["rts"]["source_key"]
        self.rts_tar_key = config["metrics"]["rts"]["target_key"]
        self.rts_compare_key = config["metrics"]["rts"]["compare_key"]

        # 定时任务相关
        self.rts_timers = []  # 存储定时器对象
        self.rts_running = False  # RTS 监控运行状态

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
        self.logger = logging.getLogger(__name__)

    def _ensure_ts_key_exists(self, ts_key):
        """
        检查 Redis TimeSeries key 是否存在

        参数:
        ts_key (str): 时间序列键名

        返回:
        bool: key 是否存在
        """
        try:
            r = self._get_redis_connection()

            # 检查 key 是否存在
            try:
                r.execute_command("TS.INFO", ts_key)
                return True  # key 已存在
            except Exception:
                # key 不存在，仅记录错误日志
                self.logger.error(f"TimeSeries key {ts_key} 不存在，请先创建该 key")
                return False

        except Exception as e:
            self.logger.error(f"检查 TimeSeries key {ts_key} 时发生错误: {e}")
            return False

    def _get_redis_connection(self):
        """
        获取 Redis 连接

        返回:
        redis.StrictRedis: Redis 连接对象
        """
        return redis.StrictRedis(
            host=self.redis_host,
            port=self.redis_port,
            password=self.redis_password,
            decode_responses=True,
        )

    def get_last_time_series_value(self, ts_key):
        """
        获取最后一个时间序列值。

        参数：
        ts_key (string): 时间序列键名

        返回：
        float: 最后一个时间序列值
        """

        # 连接到 Redis
        r = self._get_redis_connection()

        # 使用 TS.GET 命令获取时间序列最后一个值
        last_value = r.execute_command("TS.GET", ts_key)

        if last_value is not None:
            _, value = last_value
            return value
        else:
            return None

    def get_hourly_sum_from_start(self, ts_key):
        """
        获取当前小时整点到现在的数据之和（日本时区）
        使用 Redis TimeSeries 的 AGGREGATION SUM 功能

        参数:
        ts_key (str): 时间序列键名

        返回:
        float: 数据之和，如果没有数据返回 0
        """
        try:
            # 检查 TimeSeries key 是否存在
            if not self._ensure_ts_key_exists(ts_key):
                self.logger.error(f"TimeSeries key {ts_key} 不存在，无法获取数据")
                return 0.0

            # 获取日本时间
            now_jst = pendulum.now("Asia/Tokyo")
            # 当前小时的整点时间
            hour_start = now_jst.start_of("hour")

            # 转换为 Redis 时间戳格式（毫秒）
            start_timestamp = int(hour_start.timestamp() * 1000)
            end_timestamp = int(now_jst.timestamp() * 1000)

            # 连接到 Redis
            r = self._get_redis_connection()

            # 使用 TS.RANGE 命令配合 AGGREGATION SUM 获取数据之和
            # 聚合时间窗口设为1小时（3600000毫秒），对齐到开始时间戳
            data = r.execute_command(
                "TS.RANGE",
                ts_key,
                start_timestamp,
                end_timestamp,
                "AGGREGATION",
                "SUM",
                3600000,
                "ALIGN",
                start_timestamp,
            )

            # 获取聚合结果
            if data and len(data) > 0:
                # data 返回的是聚合后的结果，通常只有一个数据点 [timestamp, sum_value]
                total_sum = float(data[0][1])
                self.logger.info(
                    f"获取 {ts_key} 从 {hour_start} 到 {now_jst} 的数据之和: {total_sum} (使用 Redis TS AGGREGATION SUM)"
                )
                return total_sum
            else:
                self.logger.warning(f"未找到 {ts_key} 在指定时间范围内的数据")
                return 0.0

        except Exception as e:
            self.logger.error(f"获取小时数据之和时发生错误: {e}")
            return 0.0

    def write_to_redis_ts(self, ts_key, value, timestamp=None):
        """
        写入数据到 Redis TimeSeries

        参数:
        ts_key (str): 时间序列键名
        value (float): 要写入的值
        timestamp (int, optional): 时间戳（毫秒），如果为 None 则使用当前时间

        返回:
        bool: 写入是否成功
        """
        try:
            # 检查 TimeSeries key 是否存在
            if not self._ensure_ts_key_exists(ts_key):
                self.logger.error(f"TimeSeries key {ts_key} 不存在，无法写入数据")
                return False

            # 如果没有提供时间戳，使用当前日本时间
            if timestamp is None:
                now_jst = pendulum.now("Asia/Tokyo")
                timestamp = int(now_jst.timestamp() * 1000)

            # 连接到 Redis
            r = self._get_redis_connection()

            # 使用 TS.ADD 命令写入数据
            r.execute_command("TS.ADD", ts_key, timestamp, value)

            self.logger.info(f"成功写入数据到 {ts_key}: 时间戳={timestamp}, 值={value}")
            return True

        except Exception as e:
            self.logger.error(f"写入数据到 {ts_key} 时发生错误: {e}")
            return False

    def get_last_week_same_hour_last_value(self, ts_key):
        """
        获取上周同日同时间段的 Last 值（日本时区）
        使用 Redis TimeSeries 的 AGGREGATION LAST 功能

        参数:
        ts_key (str): 时间序列键名

        返回:
        float: 上周同日同时间段的最后一个值，如果没有数据返回 0
        """
        try:
            # 检查 TimeSeries key 是否存在
            if not self._ensure_ts_key_exists(ts_key):
                self.logger.error(f"TimeSeries key {ts_key} 不存在，无法获取数据")
                return 0.0

            # 获取当前日本时间
            now_jst = pendulum.now("Asia/Tokyo")
            # 当前小时的整点时间
            current_hour_start = now_jst.start_of("hour")

            # 计算上周同日同时间
            last_week_same_time = current_hour_start.subtract(weeks=1)
            last_week_hour_end = last_week_same_time.add(hours=1)

            # 转换为 Redis 时间戳格式（毫秒）
            start_timestamp = int(last_week_same_time.timestamp() * 1000)
            end_timestamp = int(last_week_hour_end.timestamp() * 1000)

            # 连接到 Redis
            r = self._get_redis_connection()

            # 使用 TS.RANGE 命令配合 AGGREGATION LAST 获取最后一个值
            # 聚合时间窗口设为1小时（3600000毫秒），对齐到开始时间戳
            data = r.execute_command(
                "TS.RANGE",
                ts_key,
                start_timestamp,
                end_timestamp,
                "AGGREGATION",
                "LAST",
                3600000,
                "ALIGN",
                start_timestamp,
            )

            # 获取聚合结果
            if data and len(data) > 0:
                # data 返回的是聚合后的结果，通常只有一个数据点 [timestamp, last_value]
                last_value = float(data[0][1])
                self.logger.info(
                    f"获取 {ts_key} 上周同日 {last_week_same_time} ~ {last_week_hour_end} 的 Last 值: {last_value} (使用 Redis TS AGGREGATION LAST)"
                )
                return last_value
            else:
                self.logger.warning(f"未找到 {ts_key} 在上周同日同时间范围内的数据")
                return 0.0

        except Exception as e:
            self.logger.error(f"获取上周同日同时间 Last 值时发生错误: {e}")
            return 0.0

    def calc_sliding_window_avg_value(self, ts_key, sliding_window_size: int):
        """
        计算滑动时间窗口的平均值。

        参数：
        ts_key (string): 时间序列键名

        返回：
        float: 滑动时间窗口的平均值
        """
        # 滑动时间窗口的大小（例如，过去 1 分钟的数据）
        window_size = pendulum.duration(seconds=float(sliding_window_size))
        # 获取当前时间
        now = pendulum.now("UTC")
        # 计算窗口的开始时间
        start_time = now - window_size
        # 将时间转换为 Redis 时间戳格式（毫秒）
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)

        # 连接到 Redis
        r = self._get_redis_connection()

        # 使用 TS.RANGE 命令获取指定时间窗口内的数据
        data = r.execute_command("TS.RANGE", ts_key, start_timestamp, end_timestamp)

        # 计算平均值
        if data:
            values = [float(value) for _, value in data]
            avg_value = sum(values) / len(values)
            return avg_value
        else:
            return None

    def write_to_prometheus_metrics(self):
        """
        从 Redis 时间序列中读取数据并将其写入 Prometheus 指标
        """

        # 启动 Prometheus HTTP 服务器
        start_http_server(port=self.http_server_port, addr=self.http_server_addr)

        while True:
            for idx, (gauge, redis_ts_keys) in enumerate(
                zip(self.gauges, self.redis_ts_keys, strict=True)
            ):
                # 滑动时间窗口的大小（秒）
                sliding_window_size = self.sliding_window_sizes[idx]
                match gauge._name:
                    case "kenta_scheduling_status":
                        for redis_ts_key in redis_ts_keys:
                            key_value = self.get_last_time_series_value(redis_ts_key[0])
                            if key_value is not None:
                                # 将值设置到 Prometheus 指标中
                                gauge.labels(status=redis_ts_key[1]).set(key_value)
                    case "kenta_scheduling_status_transform":
                        for redis_ts_key in redis_ts_keys:
                            key_value = self.calc_sliding_window_avg_value(
                                redis_ts_key[0], sliding_window_size
                            )
                            if key_value is not None:
                                # 将值设置到 Prometheus 指标中
                                gauge.labels(status=redis_ts_key[1]).set(key_value)
                    case "kenta_scheduling_status_transform_custom_sec":
                        for redis_ts_key in redis_ts_keys:
                            key_value = self.calc_sliding_window_avg_value(
                                redis_ts_key[0], sliding_window_size
                            )
                            if key_value is not None:
                                # 将值设置到 Prometheus 指标中
                                gauge.labels(status=redis_ts_key[1]).set(key_value)
                    case _:
                        pass

            # 获取一次的间隔时间
            time.sleep(self.interval)

    def _minute_task(self):
        """
        每分钟执行的任务：查询 rts_src_key 并写入 rts_tar_key
        """
        try:
            # 查询当前小时整点到现在的数据之和
            hourly_sum = self.get_hourly_sum_from_start(self.rts_src_key)

            # 写入到目标 key
            success = self.write_to_redis_ts(self.rts_tar_key, hourly_sum)

            if success:
                self.logger.info(
                    f"每分钟任务完成: 从 {self.rts_src_key} 获取数据 {hourly_sum}，写入到 {self.rts_tar_key}"
                )
            else:
                self.logger.error("每分钟任务失败: 数据写入失败")

        except Exception as e:
            self.logger.error(f"每分钟任务执行时发生错误: {e}")

        # 调度下一次执行（下一分钟的开始）
        if self.rts_running:
            self._schedule_minute_task()

    def _hour_task(self):
        """
        每小时执行的任务：查询上周同日同时间的 Last 值并写入 rts_compare_key
        """
        try:
            # 查询上周同日同时间的 Last 值
            last_week_value = self.get_last_week_same_hour_last_value(self.rts_tar_key)

            # 写入到对比 key
            success = self.write_to_redis_ts(self.rts_compare_key, last_week_value)

            if success:
                self.logger.info(
                    f"每小时任务完成: 从 {self.rts_tar_key} 获取上周数据 {last_week_value}，写入到 {self.rts_compare_key}"
                )
            else:
                self.logger.error("每小时任务失败: 数据写入失败")

        except Exception as e:
            self.logger.error(f"每小时任务执行时发生错误: {e}")

        # 调度下一次执行（下一小时的整点）
        if self.rts_running:
            self._schedule_hour_task()

    def _schedule_minute_task(self):
        """
        调度下一次分钟任务
        """
        # 计算到下一分钟开始的秒数
        now_jst = pendulum.now("Asia/Tokyo")
        next_minute = now_jst.add(minutes=1).start_of("minute")
        delay = (next_minute - now_jst).total_seconds()

        # 创建定时器
        timer = threading.Timer(delay, self._minute_task)
        timer.daemon = True
        timer.start()
        self.rts_timers.append(timer)

        self.logger.info(f"已调度下一次分钟任务，将在 {delay:.1f} 秒后执行")

    def _schedule_hour_task(self):
        """
        调度下一次小时任务
        """
        # 计算到下一小时整点的秒数
        now_jst = pendulum.now("Asia/Tokyo")
        next_hour = now_jst.add(hours=1).start_of("hour")
        delay = (next_hour - now_jst).total_seconds()

        # 创建定时器
        timer = threading.Timer(delay, self._hour_task)
        timer.daemon = True
        timer.start()
        self.rts_timers.append(timer)

        self.logger.info(f"已调度下一次小时任务，将在 {delay:.1f} 秒后执行")

    def start_rts_monitoring(self):
        """
        启动 RTS 监控任务
        """
        if self.rts_running:
            self.logger.warning("RTS 监控已经在运行中")
            return

        self.rts_running = True
        self.logger.info("启动 RTS 监控任务")

        # 立即执行一次分钟任务，然后开始调度
        self._minute_task()

        # 检查是否在整点，如果是则立即执行小时任务
        now_jst = pendulum.now("Asia/Tokyo")
        if now_jst.minute == 0 and now_jst.second < 30:  # 在整点的前30秒内
            self._hour_task()
        else:
            # 否则调度到下一个整点
            self._schedule_hour_task()

    def stop_rts_monitoring(self):
        """
        停止 RTS 监控任务
        """
        self.rts_running = False

        # 取消所有定时器
        for timer in self.rts_timers:
            timer.cancel()
        self.rts_timers.clear()

        self.logger.info("已停止 RTS 监控任务")


def main(filepath: Union[Path, str]):
    """
    应用主函数。

    参数：
    filepath (Union[Path, str]): 配置文件路径
    """
    file_path = Path(filepath) if isinstance(filepath, str) else filepath
    metrics = NeoXMetrics(file_path)

    # 启动 RTS 任务
    metrics.start_rts_monitoring()

    # 启动 Prometheus 指标监控（这个会进入无限循环）
    metrics.write_to_prometheus_metrics()


if __name__ == "__main__":
    # 当用作主程序时，以当前目录下的 metrics.toml 作为配置文件启动程序
    main(Path.cwd().joinpath("metrics.toml"))
